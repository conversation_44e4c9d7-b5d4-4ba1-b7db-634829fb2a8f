# Laravel CRM API 项目 Makefile
# 提供开发、测试、部署等常用命令的快捷方式


# =============================================================================
# 声明所有伪目标
# =============================================================================
.PHONY: help install update serve serve-bg stop
.PHONY: dev-setup dev-reset dev-status fresh-install
.PHONY: migrate migrate-fresh migrate-rollback migrate-status seed db-reset
.PHONY: test test-unit test-feature test-coverage test-parallel test-mysql-compatibility
.PHONY: quick-test full-check
.PHONY: cache-clear config-clear route-clear view-clear optimize reset-all
.PHONY: lint lint-fix analyze analyze-baseline analyze-clear-cache analyze-level security-check
.PHONY: queue-work queue-restart queue-failed queue-clean queue-status
.PHONY: tinker routes env about logs
.PHONY: logs-clean-business logs-clean-business-dry logs-view-business logs-list-business
.PHONY: telescope-install telescope-publish telescope-clear telescope-prune telescope-status
.PHONY: telescope-enable telescope-disable telescope-test telescope-config
.PHONY: make-controller make-model make-migration make-request make-resource make-service make-repository
.PHONY: deploy-prepare deploy-check production-optimize
.PHONY: docker-build docker-up docker-down docker-logs
.PHONY: backup-db restore-db


# 默认目标：显示帮助信息
help: ## 显示所有可用命令
	@echo "Laravel CRM API 项目 Makefile v2.0"
	@echo "===================================="
	@echo ""
	@echo "📋 常用命令组合："
	@echo "  make cache-clear     - 清除所有缓存"
	@echo "  make dev-setup     - 完整开发环境初始化"
	@echo "  make quick-test    - 快速测试（格式+测试）"
	@echo "  make full-check    - 完整检查（格式+分析+测试+安全）"
	@echo "  make deploy-prepare - 部署前准备"
	@echo ""
	@echo "📖 所有可用命令："
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-25s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "💡 提示：使用 'make <命令名>' 执行对应命令"

# =============================================================================
# 开发环境管理
# =============================================================================

install: ## 安装项目依赖
	@echo "🔧 安装项目依赖..."
	@echo "安装 Composer 依赖..."
	composer install --no-interaction --prefer-dist --optimize-autoloader
	@echo "安装 NPM 依赖..."
	npm install
	@echo "复制环境配置文件..."
	@if [ ! -f .env ]; then cp .env.example .env; fi
	@echo "生成应用密钥..."
	php artisan key:generate
	@echo "创建存储链接..."
	php artisan storage:link
	@echo "✅ 依赖安装完成！"

fresh-install: ## 全新安装（清除所有缓存后重新安装）
	@echo "🔄 执行全新安装..."
	@if [ -d vendor ]; then rm -rf vendor; fi
	@if [ -d node_modules ]; then rm -rf node_modules; fi
	@if [ -f composer.lock ]; then rm composer.lock; fi
	@if [ -f package-lock.json ]; then rm package-lock.json; fi
	$(MAKE) install
	@echo "✅ 全新安装完成！"

dev-setup: ## 完整开发环境初始化
	@echo "🚀 初始化开发环境..."
	$(MAKE) install
	@echo "检查数据库连接..."
	@php artisan env || (echo "❌ 请先配置 .env 文件中的数据库连接" && exit 1)
	@echo "运行数据库迁移..."
	php artisan migrate
	@echo "填充测试数据..."
	php artisan db:seed
	@echo "清除缓存..."
	$(MAKE) cache-clear
	@echo "✅ 开发环境初始化完成！"
	@echo "🌐 运行 'make serve' 启动开发服务器"

dev-reset: ## 重置开发环境
	@echo "🔄 重置开发环境..."
	$(MAKE) cache-clear
	$(MAKE) db-reset
	@echo "✅ 开发环境重置完成！"

dev-status: ## 检查开发环境状态
	@echo "📊 开发环境状态检查"
	@echo "===================="
	@echo "PHP 版本: $$(php -v | head -n1)"
	@echo "Composer 版本: $$(composer --version)"
	@echo "Node.js 版本: $$(node --version 2>/dev/null || echo '未安装')"
	@echo "NPM 版本: $$(npm --version 2>/dev/null || echo '未安装')"
	@echo ""
	@echo "Laravel 信息:"
	@php artisan --version
	@echo ""
	@echo "数据库连接:"
	@php artisan env | grep DB_ || echo "数据库配置未找到"
	@echo ""
	@echo "存储目录权限:"
	@ls -la storage/ | head -5
	@echo ""
	@echo "缓存状态:"
	@echo "配置缓存: $$([ -f bootstrap/cache/config.php ] && echo '已缓存' || echo '未缓存')"
	@echo "路由缓存: $$([ -f bootstrap/cache/routes-v7.php ] && echo '已缓存' || echo '未缓存')"

update: ## 更新项目依赖
	@echo "🔄 更新项目依赖..."
	@echo "更新 Composer 依赖..."
	composer update --no-interaction --prefer-dist --optimize-autoloader
	@echo "更新 NPM 依赖..."
	npm update
	@echo "清除缓存..."
	$(MAKE) cache-clear
	@echo "✅ 依赖更新完成！"

serve: ## 启动开发服务器
	@echo "🌐 启动 Laravel 开发服务器..."
	@echo "访问地址: http://127.0.0.1:8000"
	@echo "API 文档: http://127.0.0.1:8000/api/documentation"
	@echo "Telescope: http://127.0.0.1:8000/telescope"
	@echo "按 Ctrl+C 停止服务器"
	php artisan serve --host=127.0.0.1 --port=8000

serve-bg: ## 后台启动开发服务器
	@echo "🌐 后台启动 Laravel 开发服务器..."
	nohup php artisan serve --host=127.0.0.1 --port=8000 > storage/logs/serve.log 2>&1 &
	@echo "✅ 服务器已在后台启动"
	@echo "📋 访问地址: http://127.0.0.1:8000"
	@echo "📄 日志文件: storage/logs/serve.log"
	@echo "🛑 停止服务器: make stop"

stop: ## 停止后台开发服务器
	@echo "🛑 停止开发服务器..."
	@pkill -f "php artisan serve" || echo "没有运行中的开发服务器"
	@echo "✅ 开发服务器已停止"

# =============================================================================
# 数据库管理
# =============================================================================

migrate: ## 运行数据库迁移
	@echo "🗄️ 运行数据库迁移..."
	php artisan migrate
	@echo "✅ 数据库迁移完成！"

migrate-fresh: ## 重建数据库（⚠️ 会删除所有数据）
	@echo "⚠️ 重建数据库（将删除所有数据）..."
	@read -p "确认继续？(y/N): " confirm && [ "$$confirm" = "y" ] || exit 1
	php artisan migrate:fresh
	@echo "✅ 数据库重建完成！"

migrate-rollback: ## 回滚最后一次迁移
	@echo "↩️ 回滚数据库迁移..."
	php artisan migrate:rollback
	@echo "✅ 迁移回滚完成！"

migrate-status: ## 查看迁移状态
	@echo "📊 数据库迁移状态："
	php artisan migrate:status

seed: ## 填充测试数据
	@echo "🌱 填充测试数据..."
	php artisan db:seed
	@echo "✅ 测试数据填充完成！"

db-reset: ## 重置数据库并填充数据（⚠️ 会删除所有数据）
	@echo "🔄 重置数据库..."
	@read -p "确认重置数据库？(y/N): " confirm && [ "$$confirm" = "y" ] || exit 1
	php artisan migrate:fresh --seed
	@echo "✅ 数据库重置完成！"

backup-db: ## 备份数据库
	@echo "💾 备份数据库..."
	@mkdir -p storage/backups
	@DB_NAME=$$(grep DB_DATABASE .env | cut -d '=' -f2); \
	DB_USER=$$(grep DB_USERNAME .env | cut -d '=' -f2); \
	DB_PASS=$$(grep DB_PASSWORD .env | cut -d '=' -f2); \
	BACKUP_FILE="storage/backups/db_backup_$$(date +%Y%m%d_%H%M%S).sql"; \
	mysqldump -u$$DB_USER -p$$DB_PASS $$DB_NAME > $$BACKUP_FILE && \
	echo "✅ 数据库备份完成: $$BACKUP_FILE" || \
	echo "❌ 数据库备份失败"

restore-db: ## 恢复数据库（需要指定备份文件）
	@echo "📥 恢复数据库..."
	@echo "可用备份文件："
	@ls -la storage/backups/*.sql 2>/dev/null || echo "没有找到备份文件"
	@read -p "请输入备份文件路径: " backup_file; \
	if [ -f "$$backup_file" ]; then \
		DB_NAME=$$(grep DB_DATABASE .env | cut -d '=' -f2); \
		DB_USER=$$(grep DB_USERNAME .env | cut -d '=' -f2); \
		DB_PASS=$$(grep DB_PASSWORD .env | cut -d '=' -f2); \
		mysql -u$$DB_USER -p$$DB_PASS $$DB_NAME < $$backup_file && \
		echo "✅ 数据库恢复完成" || \
		echo "❌ 数据库恢复失败"; \
	else \
		echo "❌ 备份文件不存在"; \
	fi

# =============================================================================
# 代码质量检查
# =============================================================================

lint: ## 检查代码格式
	@echo "🔍 运行 Laravel Pint 代码格式检查..."
	./vendor/bin/pint --test

lint-fix: ## 自动修复代码格式
	@echo "🔧 运行 Laravel Pint 自动修复代码格式..."
	./vendor/bin/pint
	@echo "✅ 代码格式修复完成！"

analyze: ## 静态代码分析
	@echo "🔬 运行 PHPStan 静态代码分析..."
	./vendor/bin/phpstan analyse --memory-limit=512M

analyze-baseline: ## 生成 PHPStan 基线文件
	@echo "📋 生成 PHPStan 基线文件..."
	./vendor/bin/phpstan analyse --generate-baseline --memory-limit=512M
	@echo "✅ 基线文件生成完成！"

analyze-clear-cache: ## 清除 PHPStan 缓存
	@echo "🗑️ 清除 PHPStan 缓存..."
	./vendor/bin/phpstan clear-result-cache
	@if [ -d storage/phpstan ]; then rm -rf storage/phpstan/*; fi
	@echo "✅ PHPStan 缓存清除完成！"

analyze-level: ## 指定级别运行分析 (使用: make analyze-level LEVEL=8)
	@echo "🔬 运行 PHPStan 分析 (级别: $(or $(LEVEL),5))..."
	./vendor/bin/phpstan analyse --level=$(or $(LEVEL),5) --memory-limit=512M

security-check: ## 安全漏洞检查
	@echo "🔒 检查安全漏洞..."
	@echo "检查 Composer 依赖安全漏洞..."
	composer audit
	@echo "检查配置安全性..."
	@php artisan config:show | grep -i "debug\|key\|password" || true
	@echo "✅ 安全检查完成！"

quick-test: ## 快速测试（格式检查+测试）
	@echo "⚡ 执行快速测试..."
	$(MAKE) lint
	$(MAKE) test
	@echo "✅ 快速测试完成！"

full-check: ## 完整检查（格式+分析+测试+安全）
	@echo "🔍 执行完整检查..."
	$(MAKE) lint
	$(MAKE) analyze
	$(MAKE) test
	$(MAKE) security-check
	@echo "✅ 完整检查完成！"

# =============================================================================
# 测试相关
# =============================================================================

test: ## 运行所有测试
	@echo "🧪 运行所有测试..."
	php artisan test

test-unit: ## 运行单元测试
	@echo "🔬 运行单元测试..."
	php artisan test --testsuite=Unit

test-feature: ## 运行功能测试
	@echo "🎯 运行功能测试..."
	php artisan test --testsuite=Feature

test-coverage: ## 生成测试覆盖率报告
	@echo "📊 生成测试覆盖率报告..."
	php artisan test --coverage --min=80

test-parallel: ## 并行运行测试
	@echo "⚡ 并行运行测试..."
	php artisan test --parallel

test-mysql-compatibility: ## 测试 MySQL 兼容性
	@echo "🗄️ 运行 MySQL 5.7.28 兼容性测试..."
	php scripts/test-mysql-compatibility.php

test-business-log: ## 测试业务日志功能
	@echo "📝 运行业务日志功能测试..."
	php scripts/test-business-log.php

logs-clean-business: ## 清理过期的业务日志
	@echo "清理过期的业务日志文件..."
	php artisan logs:clean-business

logs-clean-business-dry: ## 预览将要清理的业务日志文件
	@echo "预览将要清理的业务日志文件..."
	php artisan logs:clean-business --dry-run

logs-view-business: ## 查看今天的业务日志
	@echo "查看今天的业务日志..."
	@if [ -f "storage/logs/business-$$(date +%Y-%m-%d).log" ]; then \
		tail -f storage/logs/business-$$(date +%Y-%m-%d).log; \
	else \
		echo "今天的业务日志文件不存在"; \
	fi

logs-list-business: ## 列出所有业务日志文件
	@echo "业务日志文件列表:"
	@ls -la storage/logs/business-*.log 2>/dev/null || echo "没有找到业务日志文件"



# =============================================================================
# 缓存和优化
# =============================================================================

cache-clear: ## 清除所有缓存
	@echo "🗑️ 清除所有缓存..."
	@echo "清除应用缓存..."
	php artisan cache:clear
	@echo "清除配置缓存..."
	php artisan config:clear
	@echo "清除路由缓存..."
	php artisan route:clear
	@echo "清除视图缓存..."
	php artisan view:clear
	@echo "清除编译文件..."
	php artisan clear-compiled
	@echo "✅ 所有缓存清除完成！"

config-clear: ## 清除配置缓存
	@echo "🗑️ 清除配置缓存..."
	php artisan config:clear

route-clear: ## 清除路由缓存
	@echo "🗑️ 清除路由缓存..."
	php artisan route:clear

view-clear: ## 清除视图缓存
	@echo "🗑️ 清除视图缓存..."
	php artisan view:clear

optimize: ## 优化应用性能
	@echo "⚡ 优化应用性能..."
	php artisan config:cache
	php artisan route:cache
	php artisan view:cache
	composer dump-autoload --optimize
	@echo "✅ 应用优化完成！"

production-optimize: ## 生产环境优化
	@echo "🚀 生产环境优化..."
	composer install --no-dev --optimize-autoloader
	php artisan config:cache
	php artisan route:cache
	php artisan view:cache
	php artisan event:cache
	@echo "✅ 生产环境优化完成！"

reset-all: ## 重置所有缓存和配置
	@echo "🔄 重置所有缓存和配置..."
	$(MAKE) cache-clear
	$(MAKE) optimize
	@echo "✅ 重置完成！"



# =============================================================================
# 队列管理
# =============================================================================

queue-work: ## 启动队列工作进程
	@echo "🔄 启动队列工作进程..."
	@echo "按 Ctrl+C 停止队列工作进程"
	php artisan queue:work

queue-restart: ## 重启队列工作进程
	@echo "🔄 重启队列工作进程..."
	php artisan queue:restart
	@echo "✅ 队列工作进程已重启！"

queue-failed: ## 查看失败的队列任务
	@echo "❌ 失败的队列任务："
	php artisan queue:failed

queue-clean: ## 清理失败的队列任务
	@echo "🗑️ 清理失败的队列任务..."
	php artisan queue:clean
	@echo "✅ 失败任务清理完成！"

queue-status: ## 查看队列状态
	@echo "📊 队列状态："
	@echo "失败任务数量："
	@php artisan queue:failed | wc -l
	@echo "队列配置："
	@php artisan config:show queue.default

# =============================================================================
# 代码生成
# =============================================================================

make-controller: ## 创建控制器 (使用: make make-controller name=LeadController)
	@if [ -z "$(name)" ]; then echo "❌ 请指定控制器名称: make make-controller name=LeadController"; exit 1; fi
	@echo "📝 创建控制器: $(name)"
	php artisan make:controller $(name) --api --resource
	@echo "✅ 控制器创建完成: app/Http/Controllers/$(name).php"

make-model: ## 创建模型 (使用: make make-model name=Lead)
	@if [ -z "$(name)" ]; then echo "❌ 请指定模型名称: make make-model name=Lead"; exit 1; fi
	@echo "📝 创建模型: $(name)"
	php artisan make:model $(name) -mfsr
	@echo "✅ 模型及相关文件创建完成:"
	@echo "  - app/Models/$(name).php"
	@echo "  - database/migrations/*_create_$(shell echo $(name) | tr '[:upper:]' '[:lower:]')s_table.php"
	@echo "  - database/factories/$(name)Factory.php"
	@echo "  - database/seeders/$(name)Seeder.php"
	@echo "  - app/Http/Resources/$(name)Resource.php"

make-migration: ## 创建迁移文件 (使用: make make-migration name=create_leads_table)
	@if [ -z "$(name)" ]; then echo "❌ 请指定迁移名称: make make-migration name=create_leads_table"; exit 1; fi
	@echo "📝 创建迁移文件: $(name)"
	php artisan make:migration $(name)
	@echo "✅ 迁移文件创建完成"

make-request: ## 创建请求验证类 (使用: make make-request name=StoreLeadRequest)
	@if [ -z "$(name)" ]; then echo "❌ 请指定请求类名称: make make-request name=StoreLeadRequest"; exit 1; fi
	@echo "📝 创建请求验证类: $(name)"
	php artisan make:request $(name)
	@echo "✅ 请求验证类创建完成: app/Http/Requests/$(name).php"

make-resource: ## 创建API资源类 (使用: make make-resource name=LeadResource)
	@if [ -z "$(name)" ]; then echo "❌ 请指定资源类名称: make make-resource name=LeadResource"; exit 1; fi
	@echo "📝 创建API资源类: $(name)"
	php artisan make:resource $(name)
	@echo "✅ API资源类创建完成: app/Http/Resources/$(name).php"

make-service: ## 创建服务类 (使用: make make-service name=LeadService)
	@if [ -z "$(name)" ]; then echo "❌ 请指定服务类名称: make make-service name=LeadService"; exit 1; fi
	@echo "📝 创建服务类: $(name)"
	@mkdir -p app/Services
	@echo "<?php\n\nnamespace App\Services;\n\n/**\n * $(name)\n */\nclass $(name)\n{\n    //\n}" > app/Services/$(name).php
	@echo "✅ 服务类创建完成: app/Services/$(name).php"

make-repository: ## 创建仓储类 (使用: make make-repository name=LeadRepository)
	@if [ -z "$(name)" ]; then echo "❌ 请指定仓储类名称: make make-repository name=LeadRepository"; exit 1; fi
	@echo "📝 创建仓储类: $(name)"
	@mkdir -p app/Repositories app/Contracts
	@echo "<?php\n\nnamespace App\Contracts;\n\n/**\n * $(name)Interface\n */\ninterface $(name)Interface\n{\n    //\n}" > app/Contracts/$(name)Interface.php
	@echo "<?php\n\nnamespace App\Repositories;\n\nuse App\Contracts\\$(name)Interface;\n\n/**\n * $(name)\n */\nclass $(name) implements $(name)Interface\n{\n    //\n}" > app/Repositories/$(name).php
	@echo "✅ 仓储类创建完成:"
	@echo "  - app/Contracts/$(name)Interface.php"
	@echo "  - app/Repositories/$(name).php"

# =============================================================================
# 实用工具
# =============================================================================

tinker: ## 启动 Tinker REPL
	@echo "🔧 启动 Tinker REPL..."
	php artisan tinker

routes: ## 显示所有路由
	@echo "🛣️ 显示所有路由："
	php artisan route:list

env: ## 显示环境信息
	@echo "🌍 环境信息："
	php artisan env

about: ## 显示应用信息
	@echo "ℹ️ 应用信息："
	php artisan about

logs: ## 查看应用日志
	@echo "📄 查看应用日志 (最新50行)："
	@tail -50 storage/logs/laravel.log

# =============================================================================
# 部署相关
# =============================================================================

deploy-check: ## 部署前检查
	@echo "🔍 部署前检查..."
	@echo "检查环境配置..."
	@if [ "$$(grep APP_ENV .env | cut -d'=' -f2)" = "production" ]; then \
		echo "✅ 生产环境配置"; \
	else \
		echo "⚠️ 非生产环境配置"; \
	fi
	@if [ "$$(grep APP_DEBUG .env | cut -d'=' -f2)" = "false" ]; then \
		echo "✅ DEBUG 已关闭"; \
	else \
		echo "❌ DEBUG 仍然开启"; \
	fi
	@if [ "$$(grep TELESCOPE_ENABLED .env | cut -d'=' -f2)" = "false" ]; then \
		echo "✅ Telescope 已禁用"; \
	else \
		echo "⚠️ Telescope 仍然启用"; \
	fi
	@echo "检查文件权限..."
	@ls -la storage/ bootstrap/cache/ | head -5
	@echo "检查数据库连接..."
	@php artisan migrate:status | head -5
	@echo "✅ 部署前检查完成！"

deploy-prepare: ## 部署前准备
	@echo "🚀 部署前准备..."
	@echo "运行完整检查..."
	$(MAKE) full-check
	@echo "禁用 Telescope..."
	$(MAKE) telescope-disable
	@echo "优化应用..."
	$(MAKE) production-optimize
	@echo "备份数据库..."
	$(MAKE) backup-db
	@echo "✅ 部署前准备完成！"

# =============================================================================
# Docker 支持 (可选)
# =============================================================================

docker-build: ## 构建 Docker 镜像
	@echo "🐳 构建 Docker 镜像..."
	@if [ -f Dockerfile ]; then \
		docker build -t crm-api:latest .; \
		echo "✅ Docker 镜像构建完成！"; \
	else \
		echo "❌ Dockerfile 不存在"; \
	fi

docker-up: ## 启动 Docker 容器
	@echo "🐳 启动 Docker 容器..."
	@if [ -f docker-compose.yml ]; then \
		docker-compose up -d; \
		echo "✅ Docker 容器启动完成！"; \
	else \
		echo "❌ docker-compose.yml 不存在"; \
	fi

docker-down: ## 停止 Docker 容器
	@echo "🐳 停止 Docker 容器..."
	@if [ -f docker-compose.yml ]; then \
		docker-compose down; \
		echo "✅ Docker 容器停止完成！"; \
	else \
		echo "❌ docker-compose.yml 不存在"; \
	fi

docker-logs: ## 查看 Docker 容器日志
	@echo "📄 查看 Docker 容器日志..."
	@if [ -f docker-compose.yml ]; then \
		docker-compose logs -f; \
	else \
		echo "❌ docker-compose.yml 不存在"; \
	fi



# =============================================================================
# Telescope 监控管理
# =============================================================================

telescope-install: ## 安装 Telescope
	@echo "🔭 安装 Laravel Telescope..."
	composer require laravel/telescope --dev
	php artisan telescope:install
	php artisan migrate
	@echo "✅ Telescope 安装完成！"
	@echo "🌐 访问地址: http://127.0.0.1:8000/telescope"

telescope-publish: ## 发布 Telescope 配置文件
	@echo "📋 发布 Telescope 配置文件..."
	php artisan vendor:publish --tag=telescope-config
	php artisan vendor:publish --tag=telescope-migrations
	@echo "✅ 配置文件发布完成！"

telescope-clear: ## 清除 Telescope 数据
	@echo "🗑️ 清除 Telescope 监控数据..."
	php artisan telescope:clear
	@echo "✅ Telescope 数据清除完成！"

telescope-prune: ## 清理过期的 Telescope 数据
	@echo "🧹 清理过期的 Telescope 数据..."
	php artisan telescope:prune --hours=48
	@echo "✅ 过期数据清理完成！"

telescope-status: ## 检查 Telescope 状态
	@echo "📊 Telescope 状态检查"
	@echo "====================="
	@echo "启用状态: $$(grep TELESCOPE_ENABLED .env 2>/dev/null | cut -d'=' -f2 || echo '未配置')"
	@echo "慢查询阈值: $$(grep TELESCOPE_SLOW_QUERY_THRESHOLD .env 2>/dev/null | cut -d'=' -f2 || echo '未配置')ms"
	@echo "访问地址: http://127.0.0.1:8000/telescope"
	@echo ""
	@echo "数据库表检查:"
	@php -r "try { \$$pdo = new PDO('mysql:host=' . env('DB_HOST', '127.0.0.1') . ';dbname=' . env('DB_DATABASE'), env('DB_USERNAME'), env('DB_PASSWORD')); \$$stmt = \$$pdo->query('SHOW TABLES LIKE \"telescope_%\"'); echo '✅ 找到 ' . \$$stmt->rowCount() . ' 个 Telescope 表'; } catch (Exception \$$e) { echo '❌ 无法连接数据库: ' . \$$e->getMessage(); }"

telescope-enable: ## 启用 Telescope
	@echo "🔭 启用 Telescope 监控..."
	@if ! grep -q "TELESCOPE_ENABLED" .env; then echo "TELESCOPE_ENABLED=true" >> .env; else sed -i '' 's/TELESCOPE_ENABLED=.*/TELESCOPE_ENABLED=true/' .env; fi
	@if ! grep -q "TELESCOPE_SLOW_QUERY_THRESHOLD" .env; then echo "TELESCOPE_SLOW_QUERY_THRESHOLD=100" >> .env; else sed -i '' 's/TELESCOPE_SLOW_QUERY_THRESHOLD=.*/TELESCOPE_SLOW_QUERY_THRESHOLD=100/' .env; fi
	@echo "✅ Telescope 已启用，慢查询阈值设置为 100ms"

telescope-disable: ## 禁用 Telescope
	@echo "🔭 禁用 Telescope 监控..."
	@sed -i '' 's/TELESCOPE_ENABLED=.*/TELESCOPE_ENABLED=false/' .env
	@echo "✅ Telescope 已禁用"

telescope-test: ## 运行 Telescope 查询监控测试
	@echo "🧪 Telescope 查询监控测试"
	@echo "========================="
	@echo "测试 API 端点:"
	@echo "  - GET /api/test/telescope/queries (查询监控测试)"
	@echo "  - GET /api/test/telescope/performance (性能测试)"
	@echo ""
	@echo "🌐 请访问 http://127.0.0.1:8000/telescope 查看监控数据"

telescope-config: ## 显示 Telescope 配置建议
	@echo "⚙️ Telescope 配置建议"
	@echo "====================="
	@echo "在 .env 文件中添加以下配置:"
	@echo ""
	@echo "# Telescope 监控配置"
	@echo "TELESCOPE_ENABLED=true"
	@echo "TELESCOPE_SLOW_QUERY_THRESHOLD=100"
	@echo ""
	@echo "# 生产环境建议"
	@echo "TELESCOPE_ENABLED=false  # 生产环境应禁用"
	@echo ""
	@echo "💡 配置完成后运行: make cache-clear"




