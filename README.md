# CRM API 系统

基于 Laravel 10 构建的客户关系管理（CRM）系统 API 服务

## 技术栈

- **框架**：Laravel 10.x
- **PHP 版本**：PHP 8.2+
- **数据库**：MySQL 5.7.28+
- **缓存**：Redis 7.2.4+
- **认证**：Laravel Sanctum
- **代码质量**：PHPStan (Level 5)、Laravel Pint
- **测试**：PHPUnit
- **监控**：Laravel Telescope
- **API 文档**：RESTful API 设计

## 环境要求

- PHP >= 8.2
- Composer >= 2.0
- MySQL >= 5.7.28
- Node.js >= 16.x (用于前端资源编译)
- Redis >= 7.2.4

## 快速开始

快速体验项目，可以按照以下步骤操作：

```bash
# 1. 克隆项目
git clone <repository-url>
cd crm-api

# 2. 一键安装
make install

# 3. 配置数据库（编辑 .env 文件）
# 设置你的数据库连接信息

# 4. 启动服务器
APP_ENV=local php artisan serve
```
然后访问 `http://127.0.0.1:8000/api/leads` 开始使用 API。

## 技术文档
[代码架构设计](https://sxnf8icmylc.feishu.cn/docx/A07sd828XoU5MmxuKVpcdjzknpc)



### 部署检查清单

- [ ] 环境变量正确配置
- [ ] 数据库迁移完成
- [ ] 应用性能优化
- [ ] Telescope 已禁用
- [ ] 日志目录权限正确
- [ ] HTTPS 配置（生产环境）
- [ ] 备份策略配置

