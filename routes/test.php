<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| 测试路由
|--------------------------------------------------------------------------
|
| 这里定义了仅在非生产环境中可用的测试路由
| 这些路由用于开发、测试和调试目的
|
*/

// 测试路由组 - 仅在非生产环境中可用
Route::prefix('test')->group(function () {

    // 健康检查测试
    Route::get('/health', function () {
        return response()->json([
            'status' => 'ok',
            'timestamp' => now()->toISOString(),
            'environment' => app()->environment(),
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
        ]);
    });

    // 配置测试
    Route::get('/config', function () {
        return response()->json([
            'app_name' => config('app.name'),
            'app_env' => config('app.env'),
            'app_debug' => config('app.debug'),
            'database_default' => config('database.default'),
            'cache_default' => config('cache.default'),
        ]);
    });

    // 数据库连接测试
    Route::get('/database', function () {
        try {
            $connections = [];

            // 测试默认连接
            $defaultConnection = config('database.default');
            $connections[$defaultConnection] = [
                'status' => 'connected',
                'driver' => config("database.connections.{$defaultConnection}.driver"),
            ];

            // 测试用户系统连接
            if (config('database.connections.user_system')) {
                try {
                    \DB::connection('user_system')->getPdo();
                    $connections['user_system'] = [
                        'status' => 'connected',
                        'driver' => config('database.connections.user_system.driver'),
                    ];
                } catch (\Exception $e) {
                    $connections['user_system'] = [
                        'status' => 'failed',
                        'error' => $e->getMessage(),
                    ];
                }
            }

            return response()->json([
                'status' => 'ok',
                'connections' => $connections,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    });

    // 缓存测试
    Route::get('/cache', function () {
        try {
            $testKey = 'test_cache_'.time();
            $testValue = 'test_value_'.rand(1000, 9999);

            // 写入缓存
            cache()->put($testKey, $testValue, 60);

            // 读取缓存
            $cachedValue = cache()->get($testKey);

            // 删除测试缓存
            cache()->forget($testKey);

            return response()->json([
                'status' => $cachedValue === $testValue ? 'ok' : 'failed',
                'driver' => config('cache.default'),
                'test_key' => $testKey,
                'expected' => $testValue,
                'actual' => $cachedValue,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    });

    // 日志测试
    Route::get('/logging', function () {
        try {
            $testMessage = 'Test log message at '.now()->toISOString();

            \Log::info($testMessage, ['test' => true]);

            return response()->json([
                'status' => 'ok',
                'message' => 'Log message written successfully',
                'test_message' => $testMessage,
                'log_channel' => config('logging.default'),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    });

    // 异常测试
    Route::get('/exception', function () {
        throw new \App\Exceptions\BusinessException('测试业务异常', 422);
    });

    // 错误配置测试
    Route::get('/error-config', function () {
        try {
            $authErrors = config('errors.Auth');
            $leadErrors = config('errors.Lead');

            return response()->json([
                'status' => 'ok',
                'auth_errors_count' => count($authErrors ?? []),
                'lead_errors_count' => count($leadErrors ?? []),
                'sample_auth_error' => $authErrors['user_not_found'] ?? null,
                'sample_lead_error' => $leadErrors['not_found'] ?? null,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    });

    // 数据库优化配置测试
    Route::get('/db-optimization-config', function () {
        try {
            $queryBuilderConfig = config('database_optimization.query_builder');
            $monitoringConfig = config('database_optimization.monitoring');

            return response()->json([
                'status' => 'ok',
                'query_builder_enabled' => $queryBuilderConfig !== null,
                'monitoring_enabled' => $monitoringConfig['enabled'] ?? false,
                'slow_query_threshold' => $monitoringConfig['slow_query_threshold'] ?? null,
                'cache_enabled' => $queryBuilderConfig['cache']['enabled'] ?? false,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    });

    // 环境信息测试
    Route::get('/environment', function () {
        return response()->json([
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'environment' => app()->environment(),
            'debug_mode' => config('app.debug'),
            'timezone' => config('app.timezone'),
            'locale' => config('app.locale'),
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
        ]);
    });

    // 权限测试（需要认证）
    Route::middleware('auth.cookie')->group(function () {
        Route::get('/auth', function (Request $request) {
            $user = $request->user();

            if (! $user) {
                return response()->json([
                    'status' => 'unauthenticated',
                    'message' => 'User not authenticated',
                ], 401);
            }

            return response()->json([
                'status' => 'authenticated',
                'user_id' => $user->id,
                'username' => $user->username,
                'realname' => $user->realname,
                'is_active' => $user->isActive(),
                'is_leader' => $user->isLeader(),
            ]);
        });
    });
});

