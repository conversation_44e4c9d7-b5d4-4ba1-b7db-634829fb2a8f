name: Build

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]

jobs:
  build:
    runs-on: ${{ matrix.operating-system }}
    strategy:
      matrix:
        operating-system: [ubuntu-latest]
        php-versions: ["5.6", "7.0", "7.1", "7.2", "7.3", "7.4", "8.1", "8.2", "8.3"]
        coverage: [false]

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}
          coverage: xdebug
          tools: composer
        env:
          fail-fast: true

      - name: Setup problem matchers for PHPUnit
        run: echo "::add-matcher::${{ runner.tool_cache }}/phpunit.json"

      - name: Validate composer
        run: composer validate

      - name: Get composer cache directory
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install dependencies
        run: composer install --no-progress

      - name: Run tests
        run: composer test
        if: ${{ !matrix.coverage }}

      - name: Run tests with coverage
        run: |
          composer test-coverage
          ls -l build/logs/
        if: ${{ matrix.coverage }}

      - name: Upload coverage results to Coveralls
        env:
          COVERALLS_REPO_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          composer global require php-coveralls/php-coveralls
          php-coveralls --coverage_clover=build/logs/clover.xml -v
        if: ${{ matrix.coverage }}
