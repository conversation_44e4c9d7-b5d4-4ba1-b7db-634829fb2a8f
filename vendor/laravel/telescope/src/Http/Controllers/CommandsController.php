<?php

namespace <PERSON><PERSON>\Telescope\Http\Controllers;

use <PERSON><PERSON>\Telescope\EntryType;
use <PERSON><PERSON>\Telescope\Watchers\CommandWatcher;

class CommandsController extends EntryController
{
    /**
     * The entry type for the controller.
     *
     * @return string
     */
    protected function entryType()
    {
        return EntryType::COMMAND;
    }

    /**
     * The watcher class for the controller.
     *
     * @return string
     */
    protected function watcher()
    {
        return CommandWatcher::class;
    }
}
