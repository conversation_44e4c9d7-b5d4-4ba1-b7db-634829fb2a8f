<?php

namespace <PERSON><PERSON>\Telescope\Http\Controllers;

use Illum<PERSON>\Bus\BatchRepository;
use <PERSON><PERSON>\Telescope\Contracts\EntriesRepository;
use <PERSON><PERSON>\Telescope\EntryType;
use <PERSON><PERSON>\Telescope\EntryUpdate;
use <PERSON><PERSON>\Telescope\Storage\EntryQueryOptions;
use <PERSON><PERSON>\Telescope\Watchers\BatchWatcher;

class QueueBatchesController extends EntryController
{
    /**
     * The entry type for the controller.
     *
     * @return string
     */
    protected function entryType()
    {
        return EntryType::BATCH;
    }

    /**
     * The watcher class for the controller.
     *
     * @return string
     */
    protected function watcher()
    {
        return BatchWatcher::class;
    }

    /**
     * Get an entry with the given ID.
     *
     * @param  \Laravel\Telescope\Contracts\EntriesRepository  $storage
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(EntriesRepository $storage, $id)
    {
        $batch = app(BatchRepository::class)->find($id);

        $storage->update(collect([
            new EntryUpdate($id, EntryType::BATCH,
                $batch?->toArray() ?: []
            ),
        ]));

        $entry = $storage->find($id)->generateAvatar();

        return response()->json([
            'entry' => $entry,
            'batch' => $storage->get(null, EntryQueryOptions::forBatchId($entry->batchId)->limit(-1)),
        ]);
    }
}
