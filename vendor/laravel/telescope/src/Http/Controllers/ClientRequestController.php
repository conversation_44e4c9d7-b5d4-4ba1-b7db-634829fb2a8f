<?php

namespace <PERSON><PERSON>\Telescope\Http\Controllers;

use <PERSON><PERSON>\Telescope\EntryType;
use <PERSON><PERSON>\Telescope\Watchers\ClientRequestWatcher;

class ClientRequestController extends EntryController
{
    /**
     * The entry type for the controller.
     *
     * @return string
     */
    protected function entryType()
    {
        return EntryType::CLIENT_REQUEST;
    }

    /**
     * The watcher class for the controller.
     *
     * @return string
     */
    protected function watcher()
    {
        return ClientRequestWatcher::class;
    }
}
