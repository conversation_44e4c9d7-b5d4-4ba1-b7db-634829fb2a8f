<p align="center"><img width="391" height="83" src="/art/logo.svg" alt="Logo Laravel Telescope"></p>

<p align="center">
<a href="https://github.com/laravel/telescope/actions"><img src="https://github.com/laravel/telescope/workflows/tests/badge.svg" alt="Build Status"></a>
<a href="https://packagist.org/packages/laravel/telescope"><img src="https://img.shields.io/packagist/dt/laravel/telescope" alt="Total Downloads"></a>
<a href="https://packagist.org/packages/laravel/telescope"><img src="https://img.shields.io/packagist/v/laravel/telescope" alt="Latest Stable Version"></a>
<a href="https://packagist.org/packages/laravel/telescope"><img src="https://img.shields.io/packagist/l/laravel/telescope" alt="License"></a>
</p>

## Introduction

<PERSON><PERSON> Telescope is an elegant debug assistant for the Laravel framework. Telescope provides insight into the requests coming into your application, exceptions, log entries, database queries, queued jobs, mail, notifications, cache operations, scheduled tasks, variable dumps and more. Telescope makes a wonderful companion to your local Laravel development environment.

<p align="center">
<img src="https://laravel.com/img/docs/telescope-example.png">
</p>

## Official Documentation

Documentation for Telescope can be found on the [Laravel website](https://laravel.com/docs/telescope).

## Contributing

Thank you for considering contributing to Telescope! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

Please review [our security policy](https://github.com/laravel/telescope/security/policy) on how to report security vulnerabilities.

## License

Laravel Telescope is open-sourced software licensed under the [MIT license](LICENSE.md).
