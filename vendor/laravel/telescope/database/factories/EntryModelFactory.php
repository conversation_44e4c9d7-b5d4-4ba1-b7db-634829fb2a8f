<?php

namespace <PERSON><PERSON>\Telescope\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use <PERSON><PERSON>\Telescope\EntryType;
use <PERSON><PERSON>\Telescope\Storage\EntryModel;

class EntryModelFactory extends Factory
{
    /**
     * Get the name of the model that is generated by the factory.
     *
     * @return string
     */
    public function modelName()
    {
        return EntryModel::class;
    }

    /**
     * {@inheritdoc}
     */
    public function definition()
    {
        return [
            'sequence' => random_int(1, 10000),
            'uuid' => $this->faker->uuid(),
            'batch_id' => $this->faker->uuid(),
            'type' => $this->faker->randomElement([
                EntryType::CACHE, EntryType::CLIENT_REQUEST, EntryType::COMMAND, EntryType::DUMP, EntryType::EVENT,
                EntryType::EXCEPTION, EntryType::JOB, EntryType::LOG, EntryType::MAIL, EntryType::MODEL,
                EntryType::NOTIFICATION, EntryType::QUERY, EntryType::REDIS, EntryType::REQUEST,
                EntryType::SCHEDULED_TASK,
            ]),
            'content' => [$this->faker->word() => $this->faker->word()],
        ];
    }
}
