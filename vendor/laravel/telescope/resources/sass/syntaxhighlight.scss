.vjs-tree {
    font-family: $font-family-monospace;
    color: #bfc7d5 !important;

    .vjs-tree__content {
        border-left: 1px dotted rgba(204, 204, 204, 0.28) !important;
    }
    .vjs-tree__node {
        cursor: pointer;
        &:hover {
            color: #20a0ff;
        }
    }
    .vjs-checkbox {
        position: absolute;
        left: -30px;
    }
    .vjs-value__null,
    .vjs-value__number,
    .vjs-value__boolean {
        color: #a291f5 !important;
    }
    .vjs-value__string {
        color: #c3e88d !important;
    }
    .vjs-key {
        color: #c3cbd3 !important;
    }
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-addition,
.hljs-attr {
    color: #13ce66;
}

.hljs-string,
.hljs-meta,
.hljs-name,
.hljs-type,
.hljs-symbol,
.hljs-bullet,
.hljs-variable,
.hljs-template-tag,
.hljs-template-variable {
    color: #c3e88d;
}

.hljs-comment,
.hljs-quote,
.hljs-deletion {
    color: #bfcbd9;
}

.hljs-title,
.hljs-number,
.hljs-literal {
    color: #a291f5 !important;
}
