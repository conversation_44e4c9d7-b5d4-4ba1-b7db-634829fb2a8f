@import 'node_modules/bootstrap/scss/bootstrap';
@import 'syntaxhighlight';

body {
    padding-bottom: 20px;
}

.container {
    max-width: 1440px;
}

html {
    min-width: 1140px;
}

[v-cloak] {
    display: none;
}

svg.icon {
    width: 1rem;
    height: 1rem;
}

.header {
    border-bottom: solid 1px $header-border-color;

    .logo {
        text-decoration: none;
        color: $logo-color;

        svg {
            width: 1.7rem;
            height: 1.7rem;
        }
    }
}

.sidebar .nav-item {
    a {
        color: $sidebar-nav-color;
        padding: 0.5rem 0.75rem;
        margin-bottom: 4px;
        border-radius: $border-radius-lg;

        svg {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 15px;
            fill: $sidebar-nav-icon-color;
        }

        &:hover {
            background-color: $sidebar-nav-hover-bg;
            color: $sidebar-nav-hover-color;
        }

        &.active {
            background-color: $sidebar-nav-active-bg;
            color: $sidebar-nav-active-color;

            svg {
                fill: $sidebar-nav-active-icon-color;
            }
        }
    }
}

.card {
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    border: none;

    .bottom-radius {
        border-bottom-left-radius: $card-border-radius;
        border-bottom-right-radius: $card-border-radius;
    }

    .card-header {
        padding-top: 0.7rem;
        padding-bottom: 0.7rem;
        background-color: $card-cap-bg;
        border-bottom: none;
        min-height: 60px;

        .btn-group {
            .btn {
                padding: 0.2rem 0.5rem;
            }
        }

        .form-control-with-icon {
            position: relative;

            .icon-wrapper {
                display: flex;
                align-items: center;
                justify-content: center;
                position: absolute;
                top: 0;
                left: 0.75rem;
                bottom: 0;

                .icon {
                    fill: $text-muted;
                }
            }

            .form-control {
                padding-left: 2.25rem;
                font-size: 0.875rem;
                border-radius: 9999px;
            }
        }
    }

    .table {
        th,
        td {
            padding: 0.75rem 1.25rem;
        }

        th {
            background-color: $table-headers-color;
            font-size: 0.875rem;
            padding: 0.5rem 1.25rem;
            border-bottom: 0;
        }

        &:not(.table-borderless) {
            td {
                border-top: 1px solid $table-border-color;
            }
        }

        &.penultimate-column-right {
            th:nth-last-child(2),
            td:nth-last-child(2) {
                text-align: right;
            }
        }

        th.table-fit,
        td.table-fit {
            width: 1%;
            white-space: nowrap;
        }
    }
}

.fill-text-color {
    fill: $body-color;
}

.fill-danger {
    fill: $danger;
}

.fill-warning {
    fill: $warning;
}

.fill-info {
    fill: $info;
}

.fill-success {
    fill: $success;
}

.fill-primary {
    fill: $primary;
}

button:hover {
    .fill-primary {
        fill: #fff;
    }
}

.btn-outline-primary.active {
    .fill-primary {
        fill: $body-bg;
    }
}

.btn-outline-primary:not(:disabled):not(.disabled).active:focus {
    box-shadow: none !important;
}

.btn-muted {
    color: $btn-muted-color;
    background: $btn-muted-bg;

    &:hover,
    &:focus {
        color: $btn-muted-hover-color;
        background: $btn-muted-hover-bg;
    }

    &.active {
        color: $btn-muted-active-color;
        background: $btn-muted-active-bg;
    }
}

.badge-secondary {
    background: $badge-secondary-bg;
    color: $badge-secondary-color;
}

.badge-success {
    background: $badge-success-bg;
    color: $badge-success-color;
}

.badge-info {
    background: $badge-info-bg;
    color: $badge-info-color;
}

.badge-warning {
    background: $badge-warning-bg;
    color: $badge-warning-color;
}

.badge-danger {
    background: $badge-danger-bg;
    color: $badge-danger-color;
}

.control-action {
    svg {
        fill: $control-action-icon-color;
        width: 1.2rem;
        height: 1.2rem;

        &:hover {
            fill: $control-action-icon-hover;
        }
    }
}

@-webkit-keyframes spin {
    from {
        -ms-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -webkit-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -ms-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -webkit-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes spin {
    from {
        -ms-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -webkit-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -ms-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -webkit-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

.spin {
    -webkit-animation: spin 2s linear infinite;
    -moz-animation: spin 2s linear infinite;
    -ms-animation: spin 2s linear infinite;
    -o-animation: spin 2s linear infinite;
    animation: spin 2s linear infinite;
}

.card {
    .nav-pills {
        background: $card-cap-bg;

        .nav-link {
            font-size: 0.9rem;
            border-radius: 0;
            padding: 0.75rem 1.25rem;
            color: $pill-link;

            &:hover,
            &:focus {
                color: $pill-link-hover;
            }

            &.active {
                background: none;
                color: $pill-link-active;
                border-bottom: solid 2px $pill-link-active;
            }
        }
    }
}

.list-enter-active:not(.dontanimate) {
    transition: background 1s linear;
}

.list-enter:not(.dontanimate),
.list-leave-to:not(.dontanimate) {
    background: $new-entries-bg;
}

.code-bg .list-enter:not(.dontanimate),
.code-bg .list-leave-to:not(.dontanimate) {
    background: $new-code-entries-bg;
}

#indexScreen {
    td {
        vertical-align: middle !important;
    }
}

.card-bg-secondary {
    background: $card-bg-secondary;
}

.code-bg {
    background: $code-bg;
}

.disabled-watcher {
    padding: 0.75rem;
    color: #fff;
    background: $danger;
}

.copy-to-clipboard {
    position: absolute;
    right: 0rem;
    top: 0rem;
    --tw-text-opacity: 1;
    color: rgb(231 232 242 / var(--tw-text-opacity));
    outline: 2px solid transparent;
    outline-offset: 2px;
    opacity: 0.7;
    z-index: 10;
}
