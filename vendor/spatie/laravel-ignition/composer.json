{"name": "spatie/laravel-ignition", "description": "A beautiful error page for Laravel applications.", "keywords": ["error", "page", "laravel", "flare"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "homepage": "https://flareapp.io/ignition", "license": "MIT", "require": {"php": "^8.1", "ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "illuminate/support": "^10.0|^11.0|^12.0", "spatie/ignition": "^1.15", "symfony/console": "^6.2.3|^7.0", "symfony/var-dumper": "^6.2.3|^7.0"}, "require-dev": {"livewire/livewire": "^2.11|^3.3.5", "mockery/mockery": "^1.5.1", "openai-php/client": "^0.8.1|^0.10", "orchestra/testbench": "8.22.3|^9.0|^10.0", "pestphp/pest": "^2.34|^3.7", "phpstan/extension-installer": "^1.3.1", "phpstan/phpstan-deprecation-rules": "^1.1.1|^2.0", "phpstan/phpstan-phpunit": "^1.3.16|^2.0", "vlucas/phpdotenv": "^5.5"}, "suggest": {"openai-php/client": "Require get solutions from OpenAI", "psr/simple-cache-implementation": "Needed to cache solutions from OpenAI"}, "config": {"sort-packages": true, "allow-plugins": {"phpstan/extension-installer": true, "pestphp/pest-plugin": true, "php-http/discovery": false}}, "extra": {"laravel": {"providers": ["Spatie\\LaravelIgnition\\IgnitionServiceProvider"], "aliases": {"Flare": "Spatie\\LaravelIgnition\\Facades\\Flare"}}}, "autoload": {"psr-4": {"Spatie\\LaravelIgnition\\": "src"}, "files": ["src/helpers.php"]}, "autoload-dev": {"psr-4": {"Spatie\\LaravelIgnition\\Tests\\": "tests"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"analyse": "vendor/bin/phpstan analyse", "baseline": "vendor/bin/phpstan --generate-baseline", "format": "vendor/bin/php-cs-fixer fix --allow-risky=yes", "test": "vendor/bin/pest", "test-coverage": "vendor/bin/phpunit --coverage-html coverage"}, "support": {"issues": "https://github.com/spatie/laravel-ignition/issues", "forum": "https://twitter.com/flareappio", "source": "https://github.com/spatie/laravel-ignition", "docs": "https://flareapp.io/docs/ignition-for-laravel/introduction"}}