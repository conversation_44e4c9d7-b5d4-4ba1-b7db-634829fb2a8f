<?php

declare(strict_types=1);

namespace <PERSON><PERSON>\Larastan\ReturnTypes\Helpers;

use Illuminate\Support\Stringable;
use Php<PERSON>arser\Node\Expr\FuncCall;
use PHPStan\Analyser\Scope;
use PHPStan\Reflection\FunctionReflection;
use PHPStan\Type\DynamicFunctionReturnTypeExtension;
use PHPStan\Type\MixedType;
use PHPStan\Type\ObjectType;
use PHPStan\Type\Type;

use function count;

class StrExtension implements DynamicFunctionReturnTypeExtension
{
    public function isFunctionSupported(FunctionReflection $functionReflection): bool
    {
        return $functionReflection->getName() === 'str';
    }

    public function getTypeFromFunctionCall(FunctionReflection $functionReflection, FuncCall $functionCall, Scope $scope): Type|null
    {
        if (count($functionCall->getArgs()) === 1) {
            return new ObjectType(Stringable::class);
        }

        return new MixedType();
    }
}
