{"name": "larastan/larastan", "type": "phpstan-extension", "description": "Larastan - Discover bugs in your code without running it. A phpstan/phpstan extension for Laravel", "keywords": ["laravel", "<PERSON><PERSON><PERSON>", "phpstan", "php", "package", "static analysis", "code analysis", "code analyse"], "license": "MIT", "authors": [{"name": "Can Vural", "email": "<EMAIL>"}], "require": {"php": "^8.0.2", "ext-json": "*", "iamcal/sql-parser": "^0.5.0", "illuminate/console": "^9.52.20 || ^10.48.28 || ^11.41.3", "illuminate/container": "^9.52.20 || ^10.48.28 || ^11.41.3", "illuminate/contracts": "^9.52.20 || ^10.48.28 || ^11.41.3", "illuminate/database": "^9.52.20 || ^10.48.28 || ^11.41.3", "illuminate/http": "^9.52.20 || ^10.48.28 || ^11.41.3", "illuminate/pipeline": "^9.52.20 || ^10.48.28 || ^11.41.3", "illuminate/support": "^9.52.20 || ^10.48.28 || ^11.41.3", "phpstan/phpstan": "^1.12.17"}, "require-dev": {"doctrine/coding-standard": "^13", "laravel/framework": "^9.52.20 || ^10.48.28 || ^11.41.3", "mockery/mockery": "^1.5.1", "nikic/php-parser": "^4.19.1", "orchestra/canvas": "^7.11.1 || ^8.11.0 || ^9.0.2", "orchestra/testbench-core": "^7.33.0 || ^8.13.0 || ^9.0.9", "phpstan/phpstan-deprecation-rules": "^1.2", "phpunit/phpunit": "^9.6.13 || ^10.5.16"}, "suggest": {"orchestra/testbench": "Using Larastan for analysing a package needs Testbench"}, "autoload-dev": {"psr-4": {"App\\": "tests/application/app/", "Database\\Factories\\": "tests/application/database/factories/", "Database\\Migrations\\": "tests/application/database/migrations/"}, "classmap": ["tests/"]}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"Larastan\\Larastan\\": "src/"}}, "config": {"sort-packages": true, "preferred-install": "dist", "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "extra": {"branch-alias": {"dev-master": "2.0-dev"}, "phpstan": {"includes": ["extension.neon"]}}, "scripts": {"test:cs": "phpcs", "test:types": "phpstan analyse --ansi --memory-limit 256M", "test:unit": "phpunit --colors=always -d memory_limit=1408M", "test": ["@test:types", "@test:unit"]}}