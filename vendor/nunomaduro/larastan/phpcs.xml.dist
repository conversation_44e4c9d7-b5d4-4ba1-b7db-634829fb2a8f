<?xml version="1.0"?>
<ruleset>
    <config name="php_version" value="80002"/>

    <arg name="basepath" value="."/>
    <arg name="extensions" value="php"/>
    <arg name="parallel" value="80"/>
    <arg name="cache" value=".phpcs-cache"/>
    <arg name="colors"/>

    <!-- Ignore warnings, show progress of the run and show sniff names -->
    <arg value="nps"/>

    <!-- Directories to be checked -->
    <file>src</file>
    <file>tests</file>

    <exclude-pattern>tests/*/data</exclude-pattern>
    <exclude-pattern>tests/enum-definition.php</exclude-pattern>
    <exclude-pattern>tests/bootstrap.php</exclude-pattern>
    <exclude-pattern>tests/application</exclude-pattern>

    <!-- Include full Doctrine Coding Standard -->
    <rule ref="Doctrine">
      <exclude name="SlevomatCodingStandard.PHP.RequireExplicitAssertion"/>
    </rule>

    <rule ref="PSR1.Methods.CamelCapsMethodName.NotCamelCaps">
      <exclude-pattern>tests/*</exclude-pattern>
    </rule>

    <rule ref="Squiz.Arrays.ArrayDeclaration.ValueNoNewline">
      <exclude-pattern>tests/Type/GenericModelPropertyTypeTest.php</exclude-pattern>
    </rule>

    <rule ref="Generic.PHP.ForbiddenFunctions">
        <properties>
            <property name="forbiddenFunctions" type="array">
                <element key="chop" value="rtrim"/>
                <element key="close" value="closedir"/>
                <element key="delete" value="unset"/>
                <element key="doubleval" value="floatval"/>
                <element key="extract" value="null"/>
                <element key="fputs" value="fwrite"/>
                <element key="ini_alter" value="ini_set"/>
                <element key="is_double" value="is_float"/>
                <element key="is_integer" value="is_int"/>
                <element key="is_long" value="is_int"/>
                <element key="is_null" value="null"/>
                <element key="is_real" value="is_float"/>
                <element key="is_writeable" value="is_writable"/>
                <element key="join" value="implode"/>
                <element key="key_exists" value="array_key_exists"/>
                <element key="pos" value="current"/>
                <element key="settype" value="null"/>
                <element key="show_source" value="highlight_file"/>
                <element key="sizeof" value="count"/>
                <element key="strchr" value="strstr"/>
            </property>
        </properties>
    </rule>
</ruleset>

