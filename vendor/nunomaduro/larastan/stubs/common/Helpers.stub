<?php

/**
 * Get / set the specified configuration value.
 *
 * If an array is passed as the key, we will assume you want to set an array of values.
 *
 * @param  array<string, mixed>|string|null  $key
 * @param  mixed  $default
 * @return ($key is null ? \Illuminate\Config\Repository : ($key is array ? null : mixed))
 */
function config($key = null, $default = null) {}

/**
 * Create a new cookie instance.
 *
 * @param  non-empty-string|null  $name
 * @param  string|null  $value
 * @param  int  $minutes
 * @param  string|null  $path
 * @param  string|null  $domain
 * @param  bool|null  $secure
 * @param  bool  $httpOnly
 * @param  bool  $raw
 * @param  string|null  $sameSite
 * @return ($name is null ? \Illuminate\Cookie\CookieJar : \Symfony\Component\HttpFoundation\Cookie)
 */
function cookie($name = null, $value = null, $minutes = 0, $path = null, $domain = null, $secure = null, $httpOnly = true, $raw = false, $sameSite = null) {}

/**
 * Log a debug message to the logs.
 *
 * @param  string|null  $message
 * @param  mixed[]  $context
 * @return ($message is null ? \Illuminate\Log\LogManager : null)
 */
function logger($message = null, array $context = []) {}

/**
 * Get a log driver instance.
 *
 * @param  string|null  $driver
 * @return ($driver is null ? \Illuminate\Log\LogManager : \Psr\Log\LoggerInterface&\Illuminate\Log\Logger )
 */
function logs($driver = null) {}

/**
 * Get an instance of the redirector.
 *
 * @param  string|null  $to
 * @param  int  $status
 * @param  array<string, mixed>  $headers
 * @param  bool|null  $secure
 * @return ($to is null ? \Illuminate\Routing\Redirector : \Illuminate\Http\RedirectResponse)
 */
function redirect($to = null, $status = 302, $headers = [], $secure = null) {}

/**
 * Get an instance of the current request or an input item from the request.
 *
 * @param  string[]|string|null  $key
 * @param  mixed  $default
 * @return ($key is null ? \Illuminate\Http\Request : ($key is array ? string[] : mixed))
 */
function request($key = null, $default = null) {}

/**
 * Get / set the specified session value.
 *
 * If an array is passed as the key, we will assume you want to set an array of values.
 *
 * @param  mixed[]|string|null  $key
 * @param  mixed  $default
 * @return ($key is null ? \Illuminate\Session\SessionManager : ($key is array ? null : mixed))
 */
function session($key = null, $default = null) {}

/**
 * Generate a url for the application.
 *
 * @param  string|null  $path
 * @param  mixed  $parameters
 * @param  bool|null  $secure
 * @return ($path is null ? \Illuminate\Contracts\Routing\UrlGenerator : string)
 */
function url($path = null, $parameters = [], $secure = null) {}

/**
 * @template TValue
 * @template TDefault
 *
 * @param callable(): TValue $callback
 * @param TDefault|(callable(\Throwable): TDefault) $rescue
 * @param bool|callable $report
 * @return TValue|TDefault
 */
function rescue(callable $callback, $rescue = null, $report = true) {}

/**
 * @template TValue
 * @param int|array<int, int> $times
 * @param callable(int): TValue $callback
 * @param int|callable(int, \Exception): int $sleepMilliseconds
 * @param null|callable(\Exception): bool $when
 * @phpstan-return TValue
 *
 * @throws \Exception
 */
function retry($times, callable $callback, $sleepMilliseconds = 0, $when = null) {}

/**
 * @template TValue
 * @param TValue $value
 * @param null|callable(TValue): mixed $callback
 * @return mixed
 */
function tap($value, $callback = null) {}

/**
 * @param  view-string|null  $view
 * @param  \Illuminate\Contracts\Support\Arrayable<array-key, mixed>|array<string, mixed>  $data
 * @param  array<string, mixed>  $mergeData
 * @return ($view is null ? \Illuminate\Contracts\View\Factory : \Illuminate\View\View)
 */
function view($view = null, $data = [], $mergeData = []) {}

/**
 * Translate the given message.
 *
 * @param  string|null  $key
 * @param  array<string, scalar>  $replace
 * @param  string|null  $locale
 * @return ($key is null ? \Illuminate\Contracts\Translation\Translator : mixed)
 */
function trans($key = null, $replace = [], $locale = null) {}

 /**
 * Translate the given message.
 *
 * @param  string|null  $key
 * @param  array<string, scalar>  $replace
 * @param  string|null  $locale
 * @return ($key is null ? null : mixed)
 */
function __($key = null, $replace = [], $locale = null) {}

/**
 * Provide access to optional objects.
 *
 * @template TValue of mixed
 * @template TReturn of mixed
 *
 * @param  TValue  $value
 * @param  ?callable(TValue): TReturn  $callback
 * @return ($callback is null ? mixed : ($value is null ? null : TReturn))
 */
function optional($value = null, callable $callback = null) {}

/**
 * Transform the given value if it is present.
 *
 * @template TValue of mixed
 * @template TReturn of mixed
 * @template TDefault of mixed
 *
 * @param TValue|null $value
 * @param callable(TValue): TReturn $callback
 * @param TDefault|callable(TValue|null): TDefault $default
 * @return ($value is empty ? TDefault : TReturn)
 */
function transform($value, callable $callback, $default = null): mixed {}

/**
 * Determine if a value is "filled".
 *
 * @phpstan-assert-if-true !=null $value
 *
 * @param  mixed  $value
 * @return bool
 */
function filled($value) {}

/**
 * Determine if the given value is "blank".
 *
 * @phpstan-assert-if-false !=null $value
 *
 * @param  mixed  $value
 * @return bool
 */
function blank($value) {}

/**
 * Gets the value of an environment variable.
 *
 * @template TDefault
 *
 * @param  string  $key
 * @param  TDefault  $default
 * @return bool|string|TDefault
 */
function env($key, $default = null) {}
