<?php

namespace Illuminate\Contracts\Auth\Access
    {

    interface Gate
    {
        /**
         * Get a guard instance for the given user.
         *
         * @param  \Illuminate\Contracts\Auth\Authenticatable|mixed  $user
         * @return \Illuminate\Contracts\Auth\Access\Gate
         */
        public function forUser($user);
    }
}

namespace Illuminate\Auth\Access
{

    class Gate implements \Illuminate\Contracts\Auth\Access\Gate
    {
        /**
         * Get a guard instance for the given user.
         *
         * @param  \Illuminate\Contracts\Auth\Authenticatable|mixed  $user
         * @return \Illuminate\Contracts\Auth\Access\Gate
         */
        public function forUser($user);
    }
}
