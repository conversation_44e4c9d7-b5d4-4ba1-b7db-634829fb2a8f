<?php

namespace Illuminate\Console
{
    class Command extends \Symfony\Component\Console\Command\Command {
        /**
         * Fail the command manually.
         *
         * @param  \Throwable|string|null  $exception
         * @return never
         *
         * @throws \Illuminate\Console\ManuallyFailedException|\Throwable
         */
        public function fail(\Throwable|string|null $exception = null) {}
    }

    class ManuallyFailedException extends \RuntimeException {}
}

namespace Symfony\Component\Console\Command
{
    class Command {}
}
