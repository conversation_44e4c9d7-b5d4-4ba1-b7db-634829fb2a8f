<?php

namespace Psr\Log {
    interface LoggerInterface {}
}

namespace Monolog {
    interface ResettableInterface {}
    class Logger implements \Psr\Log\LoggerInterface, \Monolog\ResettableInterface {}
}

namespace Illuminate\Log {
    /**
     * @mixin \Illuminate\Log\LogManager
     * @mixin \Monolog\Logger
     */
    class Logger implements \Psr\Log\LoggerInterface {}

    class LogManager implements \Psr\Log\LoggerInterface {}
}
