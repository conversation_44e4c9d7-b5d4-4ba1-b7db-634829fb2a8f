<?php

namespace Illuminate\Support;

use Closure;

class Benchmark
{
    /**
     * Measure a callable or array of callables over the given number of iterations.
     *
     * @template TKey
     * @param  \Closure|array<TKey, \Closure>  $benchmarkables
     * @param  int  $iterations
     * @return ($benchmarkables is \Closure ? float : array<TKey, float>)
     */
    public static function measure(Closure|array $benchmarkables, int $iterations = 1): array|float {}
}
