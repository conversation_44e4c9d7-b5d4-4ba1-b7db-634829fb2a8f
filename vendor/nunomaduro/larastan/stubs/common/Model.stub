<?php

namespace Illuminate\Database\Eloquent;

/**
 * @implements \ArrayAccess<string, mixed>
 */
abstract class Model implements \JsonSerializable, \ArrayAccess
{
    /**
     * @var array<string, mixed>
     */
    protected $attributes = [];

    /**
     * @var array<string, string>
     */
    protected $casts = [];

    /**
     * @var list<string>
     */
    protected $hidden = [];

    /**
     * @var list<string>
     */
    protected $fillable = [];

    /**
     * @var list<string>
     */
    protected $visible = [];

    /**
     * @var list<string>
     */
    protected $appends = [];

    /**
     * @var list<string>
     */
    protected $with = [];

    /**
     * @var list<string>
     */
    protected $withCount = [];

    /**
     * Update the model in the database.
     *
     * @param  array<array-key, mixed>  $options
     * @return bool
     */
    public function save(array $options = []);

    /**
     * Update the model in the database.
     *
     * @param  array<model-property<static>, mixed>  $attributes
     * @param  array<int|string, mixed>  $options
     * @return bool
     */
    public function update(array $attributes = [], array $options = []);

    /**
     * Begin querying a model with eager loading.
     *
     * @param  string|array<int|string, (\Closure)|string|array<int|string, string>>  $relations
     * @return \Illuminate\Database\Eloquent\Builder<static>
     */
    public static function with($relations);
}

/**
 * @template TModel of Model
 */
class ModelNotFoundException extends \RuntimeException {}
