<?php

namespace Illuminate\Database\Eloquent\Relations;

/**
 * @template TRelatedModel of \Illuminate\Database\Eloquent\Model
 * @extends HasOneOrMany<TRelatedModel>
 */
abstract class MorphOneOrMany extends HasOneOrMany
{
    /**
     * @param array<model-property<TRelatedModel>, mixed> $attributes
     *
     * @phpstan-return TRelatedModel
     */
    public function create(array $attributes = []);
}
