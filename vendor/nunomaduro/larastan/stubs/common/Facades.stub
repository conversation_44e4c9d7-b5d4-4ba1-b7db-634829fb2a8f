<?php

namespace Illuminate\Support\Facades;

abstract class Facade {}

/**
 * @mixin \Illuminate\Redis\Connections\Connection
 * @mixin \Redis
 * @mixin \Illuminate\Redis\RedisManager
 * @mixin \Illuminate\Contracts\Redis\Factory
 */
class Redis {}

/**
 * @mixin \Illuminate\Database\DatabaseManager
 * @mixin \Illuminate\Database\Connection
 * @mixin \Illuminate\Database\ConnectionInterface
 */
class DB extends Facade {}

/**
 * @mixin \Illuminate\Queue\QueueManager
 * @mixin \Illuminate\Queue\Queue
 */
class Queue extends Facade {}

/**
 * @mixin \Illuminate\Log\Logger
 */
class Log extends Facade {}

/**
 * @mixin \Illuminate\Http\Request
 */
class Request extends Facade {}

/**
 * @mixin \Illuminate\Foundation\Console\Kernel
 */
class Artisan extends Facade {}

/**
 * @mixin \Illuminate\Cookie\CookieJar
 */
class Cookie extends Facade {
    /**
     * Retrieve a cookie from the request.
     *
     * @template TDefault
     * @param  string|null  $key
     * @param  TDefault  $default
     * @return ($key is null ? array<string, string> : string|TDefault)
     */
    public static function get($key = null, $default = null);
}
