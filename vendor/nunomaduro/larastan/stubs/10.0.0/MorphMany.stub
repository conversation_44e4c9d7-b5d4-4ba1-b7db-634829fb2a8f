<?php

namespace Illuminate\Database\Eloquent\Relations;

/**
 * @template TRelatedModel of \Illuminate\Database\Eloquent\Model
 * @extends MorphOneOrMany<TRelatedModel>
 */
class MorphMany extends MorphOneOrMany
{
    /**
     * @param array<model-property<TRelatedModel>, mixed> $attributes
     *
     * @phpstan-return TRelatedModel
     */
    public function create(array $attributes = []);

    /**
     * Get the results of the relationship.
     *
     * @phpstan-return \Traversable<int, TRelatedModel>
     */
    public function getResults();

    /**
     * Convert the relationship to a "morph one" relationship.
     *
     * @return MorphOne<TRelatedModel>
     */
    public function one();
}
