<?php

namespace Illuminate\Database\Eloquent;

/**
 * @template TModelClass of Model
 * @property-read static $orWhere
 * @property-read static $whereNot
 * @property-read static $orWhereNot
 */
class Builder
{
    /**
     * Create and return an un-saved model instance.
     *
     * @phpstan-param array<model-property<TModelClass>, mixed> $attributes
     * @phpstan-return TModelClass
     */
    public function make(array $attributes = []);

    /** @phpstan-return TModelClass */
    public function getModel();

    /**
     * @phpstan-param array<model-property<TModelClass>, mixed> $attributes
     * @phpstan-return TModelClass
     */
    public function create(array $attributes = []);

    /**
     * Create a collection of models from plain arrays.
     *
     * @param  array<mixed>  $items
     * @phpstan-return \Illuminate\Database\Eloquent\Collection<int, TModelClass>
     */
    public function hydrate(array $items);

    /**
     * Create a collection of models from a raw query.
     *
     * @param  string  $query
     * @param  array<mixed>  $bindings
     * @phpstan-return \Illuminate\Database\Eloquent\Collection<int, TModelClass>
     */
    public function fromQuery($query, $bindings = []);

    /**
     * Find a model by its primary key.
     *
     * @param  mixed  $id
     * @param  array<int, (model-property<TModelClass>|'*')>|model-property<TModelClass>|'*'  $columns
     * @phpstan-return ($id is (\Illuminate\Contracts\Support\Arrayable<array-key, mixed>|array<mixed>) ? \Illuminate\Database\Eloquent\Collection<int, TModelClass> : TModelClass|null)
     */
    public function find($id, $columns = ['*']);

    /**
     * Find multiple models by their primary keys.
     *
     * @param  \Illuminate\Contracts\Support\Arrayable<array-key, mixed>|array<mixed>  $ids
     * @param  array<int, (model-property<TModelClass>|'*')>|model-property<TModelClass>|'*'  $columns
     * @return \Illuminate\Database\Eloquent\Collection<int, TModelClass>
     */
    public function findMany($ids, $columns = ['*']);

    /**
     * Find a model by its primary key or throw an exception.
     *
     * @param  mixed  $id
     * @param  array<int, (model-property<TModelClass>|'*')>|model-property<TModelClass>|'*'  $columns
     * @phpstan-return ($id is (\Illuminate\Contracts\Support\Arrayable<array-key, mixed>|array<mixed>) ? \Illuminate\Database\Eloquent\Collection<int, TModelClass> : TModelClass)
     *
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
     */
    public function findOrFail($id, $columns = ['*']);

    /**
     * Find a model by its primary key or return fresh model instance.
     *
     * @template T
     * @param  T  $id
     * @param  array<int, (model-property<TModelClass>|'*')>|model-property<TModelClass>|'*'  $columns
     * @phpstan-return ($id is array ? \Illuminate\Database\Eloquent\Collection<int, TModelClass> : TModelClass)
     */
    public function findOrNew($id, $columns = ['*']);

    /**
     * Execute the query and get the first result.
     *
     * @param  array<int, (model-property<TModelClass>|'*')>|model-property<TModelClass>|'*'  $columns
     * @return TModelClass|null
     */
    public function first($columns = ['*']);

    /**
     * Get the first record matching the attributes or instantiate it.
     *
     * @param  array<model-property<TModelClass>, mixed>  $attributes
     * @param  array<model-property<TModelClass>, mixed>  $values
     * @phpstan-return TModelClass
     */
    public function firstOrNew(array $attributes = [], array $values = []);

    /**
     * Get the first record matching the attributes or create it.
     *
     * @param  array<model-property<TModelClass>, mixed>  $attributes
     * @param  array<model-property<TModelClass>, mixed>  $values
     * @phpstan-return TModelClass
     */
    public function firstOrCreate(array $attributes, array $values = []);

    /**
     * Attempt to create the record. If a unique constraint violation occurs, attempt to find the matching record.
     *
     * @param  array<model-property<TModelClass>, mixed>  $attributes
     * @param  array<model-property<TModelClass>, mixed>  $values
     * @phpstan-return TModelClass
     */
    public function createOrFirst(array $attributes = [], array $values = []);

    /**
     * Create or update a record matching the attributes, and fill it with values.
     *
     * @param  array<model-property<TModelClass>, mixed>  $attributes
     * @param  array<model-property<TModelClass>, mixed>  $values
     * @phpstan-return TModelClass
     */
    public function updateOrCreate(array $attributes, array $values = []);

    /**
     * @param  array<model-property<TModelClass>, mixed>  $attributes
     * @phpstan-return TModelClass
     */
    public function forceCreate(array $attributes);

    /**
     * @param  array<model-property<TModelClass>, mixed>  $values
     * @return int
     */
    public function update(array $values);

    /**
     * Execute the query and get the first result or throw an exception.
     *
     * @param  array<int, (model-property<TModelClass>|'*')>|model-property<TModelClass>|'*'  $columns
     * @phpstan-return TModelClass
     *
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
     */
    public function firstOrFail($columns = ['*']);

    /**
     * Execute the query and get the first result if it's the sole matching record.
     *
     * @param  array<int, (model-property<TModelClass>|'*')>|model-property<TModelClass>|'*'  $columns
     * @phpstan-return TModelClass
     */
    public function sole($columns = ['*']);

    /**
     * Execute the query and get the first result or call a callback.
     *
     * @param  \Closure|array<int, (model-property<TModelClass>|'*')>  $columns
     * @param  \Closure|null  $callback
     * @phpstan-return TModelClass|mixed
     */
    public function firstOr($columns = ['*'], \Closure $callback = null);

    /**
     * Add a relationship count / exists condition to the query.
     *
     * @template TRelatedModel of Model
     * @param  \Illuminate\Database\Eloquent\Relations\Relation<TRelatedModel>|string  $relation
     * @param  string  $operator
     * @param  int  $count
     * @param  string  $boolean
     * @param  \Closure|null  $callback
     * @return static
     *
     * @throws \RuntimeException
     */
    public function has($relation, $operator = '>=', $count = 1, $boolean = 'and', \Closure $callback = null);

    /**
     * Add a relationship count / exists condition to the query with an "or".
     *
     * @param  string  $relation
     * @param  string  $operator
     * @param  int  $count
     * @return static
     */
    public function orHas($relation, $operator = '>=', $count = 1);

    /**
     * Add a relationship count / exists condition to the query.
     *
     * @param  string  $relation
     * @param  string  $boolean
     * @param  \Closure|null  $callback
     * @return static
     */
    public function doesntHave($relation, $boolean = 'and', \Closure $callback = null);

    /**
     * Add a relationship count / exists condition to the query with an "or".
     *
     * @param  string  $relation
     * @return static
     */
    public function orDoesntHave($relation);

    /**
     * Add a basic where clause to the query.
     *
     * @param  (\Closure(static): mixed)|model-property<TModelClass>|array<int|model-property<TModelClass>, mixed>|\Illuminate\Contracts\Database\Query\Expression  $column
     * @param  mixed  $operator
     * @param  mixed  $value
     * @param  string  $boolean
     * @return static
     */
    public function where($column, $operator = null, $value = null, $boolean = 'and');

    /**
     * Add an "or where" clause to the query.
     *
     * @param  (\Closure(static): mixed)|model-property<TModelClass>|array<int|model-property<TModelClass>, mixed>|\Illuminate\Contracts\Database\Query\Expression  $column
     * @param  mixed  $operator
     * @param  mixed  $value
     * @return static
     */
    public function orWhere($column, $operator = null, $value = null);

    /**
     * Add a relationship count / exists condition to the query with where clauses.
     *
     * @param  string  $relation
     * @param  \Closure|null  $callback
     * @param  string  $operator
     * @param  int  $count
     * @return static
     */
    public function whereHas($relation, \Closure $callback = null, $operator = '>=', $count = 1);

    /**
     * Add a relationship count / exists condition to the query with where clauses.
     *
     * Also load the relationship with same condition.
     *
     * @param  string  $relation
     * @param  \Closure|null  $callback
     * @param  string  $operator
     * @param  int  $count
     * @return static
     */
    public function withWhereHas($relation, \Closure $callback = null, $operator = '>=', $count = 1);

    /**
     * Add a relationship count / exists condition to the query with where clauses and an "or".
     *
     * @param  string  $relation
     * @param  \Closure|null  $callback
     * @param  string  $operator
     * @param  int  $count
     * @return static
     */
    public function orWhereHas($relation, \Closure $callback = null, $operator = '>=', $count = 1);

    /**
     * Add an "or where" clause to a relationship query.
     *
     * @param  string  $relation
     * @param  \Closure|string|array<int, string>|\Illuminate\Contracts\Database\Query\Expression  $column
     * @param  mixed  $operator
     * @param  mixed  $value
     * @return static
     */
    public function orWhereRelation($relation, $column, $operator = null, $value = null);

    /**
     * Add a polymorphic relationship count / exists condition to the query.
     *
     * @template TRelatedModel of Model
     * @template TChildModel of Model
     * @param  \Illuminate\Database\Eloquent\Relations\MorphTo<TRelatedModel, TChildModel>|string  $relation
     * @param  string|array<string>  $types
     * @param  string  $operator
     * @param  int  $count
     * @param  string  $boolean
     * @param  \Closure|null  $callback
     * @return static
     */
    public function hasMorph($relation, $types, $operator = '>=', $count = 1, $boolean = 'and', \Closure $callback = null);

    /**
     * Add a polymorphic relationship count / exists condition to the query with an "or".
     *
     * @template TRelatedModel of Model
     * @template TChildModel of Model
     * @param  \Illuminate\Database\Eloquent\Relations\MorphTo<TRelatedModel, TChildModel>|string  $relation
     * @param  string|array<string>  $types
     * @param  string  $operator
     * @param  int  $count
     * @return static
     */
    public function orHasMorph($relation, $types, $operator = '>=', $count = 1);

    /**
     * Add a polymorphic relationship count / exists condition to the query.
     *
     * @template TRelatedModel of Model
     * @template TChildModel of Model
     * @param  \Illuminate\Database\Eloquent\Relations\MorphTo<TRelatedModel, TChildModel>|string  $relation
     * @param  string|array<string>  $types
     * @param  string  $boolean
     * @param  \Closure|null  $callback
     * @return static
     */
    public function doesntHaveMorph($relation, $types, $boolean = 'and', \Closure $callback = null);

    /**
     * Add a polymorphic relationship count / exists condition to the query with an "or".
     *
     * @template TRelatedModel of Model
     * @template TChildModel of Model
     * @param  \Illuminate\Database\Eloquent\Relations\MorphTo<TRelatedModel, TChildModel>|string  $relation
     * @param  string|array<string>  $types
     * @return static
     */
    public function orDoesntHaveMorph($relation, $types);

    /**
     * Add a polymorphic relationship count / exists condition to the query with where clauses.
     *
     * @template TRelatedModel of Model
     * @template TChildModel of Model
     * @param  \Illuminate\Database\Eloquent\Relations\MorphTo<TRelatedModel, TChildModel>|string  $relation
     * @param  string|array<string>  $types
     * @param  \Closure|null  $callback
     * @param  string  $operator
     * @param  int  $count
     * @return static
     */
    public function whereHasMorph($relation, $types, \Closure $callback = null, $operator = '>=', $count = 1);

    /**
     * Add a polymorphic relationship count / exists condition to the query with where clauses and an "or".
     *
     * @template TRelatedModel of Model
     * @template TChildModel of Model
     * @param  \Illuminate\Database\Eloquent\Relations\MorphTo<TRelatedModel, TChildModel>|string  $relation
     * @param  string|array<string>  $types
     * @param  \Closure|null  $callback
     * @param  string  $operator
     * @param  int  $count
     * @return static
     */
    public function orWhereHasMorph($relation, $types, \Closure $callback = null, $operator = '>=', $count = 1);

    /**
     * Add a polymorphic relationship count / exists condition to the query with where clauses.
     *
     * @template TRelatedModel of Model
     * @template TChildModel of Model
     * @param  \Illuminate\Database\Eloquent\Relations\MorphTo<TRelatedModel, TChildModel>|string  $relation
     * @param  string|array<string>  $types
     * @param  \Closure|null  $callback
     * @return static
     */
    public function whereDoesntHaveMorph($relation, $types, \Closure $callback = null);

    /**
     * Add a polymorphic relationship count / exists condition to the query with where clauses and an "or".
     *
     * @template TRelatedModel of Model
     * @template TChildModel of Model
     * @param  \Illuminate\Database\Eloquent\Relations\MorphTo<TRelatedModel, TChildModel>|string  $relation
     * @param  string|array<string>  $types
     * @param  \Closure|null  $callback
     * @return static
     */
    public function orWhereDoesntHaveMorph($relation, $types, \Closure $callback = null);

    /**
     * Merge the where constraints from another query to the current query.
     *
     * @param  \Illuminate\Database\Eloquent\Builder<TModelClass>  $from
     * @return static
     */
    public function mergeConstraintsFrom(\Illuminate\Database\Eloquent\Builder $from);

    /**
     * Add a relationship count / exists condition to the query with where clauses and an "or".
     *
     * @param  string  $relation
     * @param  \Closure|null  $callback
     * @return static
     */
    public function orWhereDoesntHave($relation, \Closure $callback = null);

    /**
     * Add a relationship count / exists condition to the query with where clauses.
     *
     * @param  string  $relation
     * @param  \Closure|null  $callback
     * @return static
     */
    public function whereDoesntHave($relation, \Closure $callback = null);

    /**
     * Add a basic where clause to the query, and return the first result.
     *
     * @param  (\Closure(static): mixed)|model-property<TModelClass>|array<int|model-property<TModelClass>, mixed>|\Illuminate\Contracts\Database\Query\Expression  $column
     * @param  mixed  $operator
     * @param  mixed  $value
     * @param  string  $boolean
     * @phpstan-return TModelClass|null
     */
    public function firstWhere($column, $operator = null, $value = null, $boolean = 'and');

    /**
     * Execute the query as a "select" statement.
     *
     * @param  array<int, (model-property<TModelClass>|'*')>|model-property<TModelClass>|'*'  $columns
     * @phpstan-return \Illuminate\Database\Eloquent\Collection<int, TModelClass>
     */
    public function get($columns = ['*']);

    /**
     * Get the hydrated models without eager loading.
     *
     * @param  array<int, (model-property<TModelClass>|'*')>|model-property<TModelClass>|'*'  $columns
     * @phpstan-return TModelClass[]
     */
    public function getModels($columns = ['*']);

    /**
     * Get a single column's value from the first result of a query.
     *
     * @param  model-property<TModelClass>|\Illuminate\Contracts\Database\Query\Expression  $column
     * @return mixed
     */
    public function value($column);

    /**
     * Paginate the given query.
     *
     * @param  int|null  $perPage
     * @param  array<array-key, mixed>  $columns
     * @param  string  $pageName
     * @param  int|null  $page
     * @return \Illuminate\Pagination\LengthAwarePaginator<TModelClass>
     *
     * @throws \InvalidArgumentException
     */
    public function paginate($perPage = null, $columns = ['*'], $pageName = 'page', $page = null);

    /**
     * Paginate the given query into a simple paginator.
     *
     * @param  int|null  $perPage
     * @param  array<array-key, mixed>  $columns
     * @param  string  $pageName
     * @param  int|null  $page
     * @return \Illuminate\Pagination\Paginator<TModelClass>
     */
    public function simplePaginate($perPage = null, $columns = ['*'], $pageName = 'page', $page = null);

    /**
     * Paginate the given query into a cursor paginator.
     *
     * @param  int|null  $perPage
     * @param  array<array-key, mixed>  $columns
     * @param  string  $cursorName
     * @param  \Illuminate\Pagination\Cursor|string|null  $cursor
     * @return \Illuminate\Pagination\CursorPaginator<TModelClass>
     */
     public function cursorPaginate($perPage = null, $columns = ['*'], $cursorName = 'cursor', $cursor = null);

    /**
     * Get a lazy collection for the given query.
     *
     * @phpstan-return \Illuminate\Support\LazyCollection<int, TModelClass>
     */
    public function cursor();

     /**
      * Query lazily, by chunks of the given size.
      *
      * @param  int  $chunkSize
      * @return \Illuminate\Support\LazyCollection<int, TModelClass>
      *
      * @throws \InvalidArgumentException
      */
     public function lazy($chunkSize = 1000);

     /**
      * Query lazily, by chunking the results of a query by comparing IDs.
      *
      * @param  int  $chunkSize
      * @param  string|null  $column
      * @param  string|null  $alias
      * @return \Illuminate\Support\LazyCollection<int, TModelClass>
      *
      * @throws \InvalidArgumentException
      */
     public function lazyById($chunkSize = 1000, $column = null, $alias = null);

     /**
      * Query lazily, by chunking the results of a query by comparing IDs in descending order.
      *
      * @param  int  $chunkSize
      * @param  string|null  $column
      * @param  string|null  $alias
      * @return \Illuminate\Support\LazyCollection<int, TModelClass>
      *
      * @throws \InvalidArgumentException
      */
     public function lazyByIdDesc($chunkSize = 1000, $column = null, $alias = null);

    /**
     * Add a basic where clause to a relationship query.
     *
     * @param  string  $relation
     * @param  \Closure|string|array<mixed>|\Illuminate\Contracts\Database\Query\Expression  $column
     * @param  mixed  $operator
     * @param  mixed  $value
     * @return static
     */
    public function whereRelation($relation, $column, $operator = null, $value = null);

    /**
     * Gets the result of a query in chunks.
     *
     * @param  int  $count
     * @param  callable(\Illuminate\Database\Eloquent\Collection<int, TModelClass>, int): mixed $callback
     * @return bool
     */
    public function chunk($count, $callback);

    /**
     * Chunk the results of a query by comparing IDs.
     *
     * @param  int  $count
     * @param  callable(\Illuminate\Database\Eloquent\Collection<int, TModelClass>, int): mixed  $callback
     * @param  string|null  $column
     * @param  string|null  $alias
     * @return bool
     */
    public function chunkById($count, callable $callback, $column = null, $alias = null);

    /**
     * Chunk the results of a query by comparing IDs in descending order.
     *
     * @param  int  $count
     * @param  callable(\Illuminate\Database\Eloquent\Collection<int, TModelClass>, int): mixed  $callback
     * @param  string|null  $column
     * @param  string|null  $alias
     * @return bool
     */
    public function chunkByIdDesc($count, callable $callback, $column = null, $alias = null);

    /**
     * Run a map over each item while chunking.
     *
     * @template TReturn
     *
     * @param  callable(TModelClass): TReturn  $callback
     * @param  int  $count
     * @return \Illuminate\Support\Collection<int, TReturn>
     */
    public function chunkMap(callable $callback, $count = 1000);
}

class Scope {}

/**
 * @method static \Illuminate\Database\Eloquent\Builder<static> withTrashed(bool $withTrashed = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static> onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static> withoutTrashed()
 * @method static bool restore()
 * @method static static restoreOrCreate(array<string, mixed> $attributes = [], array<string, mixed> $values = [])
 * @method static static createOrRestore(array<string, mixed> $attributes = [], array<string, mixed> $values = [])
 */
trait SoftDeletes {}
