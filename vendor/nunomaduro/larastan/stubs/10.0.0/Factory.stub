<?php

namespace Illuminate\Database\Eloquent\Factories;

/**
 * @template TModel of \Illuminate\Database\Eloquent\Model
 */
class Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var class-string<TModel>
     */
    protected $model;

    /**
     * Get a new factory instance for the given attributes.
     *
     * @param  (callable(array<string, mixed>): array<string, mixed>)|array<model-property<TModel>, mixed>  $attributes
     * @return static
     */
    public static function new($attributes = []);

    /**
     * Create a single model and persist it to the database.
     *
     * @param  (callable(array<string, mixed>): array<string, mixed>)|array<model-property<TModel>, mixed> $attributes
     * @return TModel
     */
    public function createOne($attributes = []);

    /**
     * Create a single model and persist it to the database.
     *
     * @param  (callable(array<string, mixed>): array<string, mixed>)|array<model-property<TModel>, mixed> $attributes
     * @return TModel
     */
    public function createOneQuietly($attributes = []) {}

    /**
     * Create a collection of models and persist them to the database.
     *
     * @param  int|null|iterable<callable|array<model-property<TModel>, mixed>>  $records
     * @return \Illuminate\Database\Eloquent\Collection<int, TModel>
     */
    public function createMany(int|iterable|null  $records);

    /**
     * Create a collection of models and persist them to the database.
     *
     * @param  (callable(array<string, mixed>): array<string, mixed>)|array<model-property<TModel>, mixed>  $attributes
     * @param  \Illuminate\Database\Eloquent\Model|null  $parent
     * @return \Illuminate\Database\Eloquent\Collection<int, TModel>|TModel
     */
    public function create($attributes = [], ?\Illuminate\Database\Eloquent\Model $parent = null);

    /**
     * Create a collection of models and persist them to the database without dispatching any model events.
     *
     * @param  (callable(array<string, mixed>): array<string, mixed>)|array<string, mixed>  $attributes
     * @param  \Illuminate\Database\Eloquent\Model|null  $parent
     * @return \Illuminate\Database\Eloquent\Collection<int, TModel>|TModel
     */
    public function createQuietly($attributes = [], ?\Illuminate\Database\Eloquent\Model $parent = null);

    /**
     * Make a single instance of the model.
     *
     * @param  (callable(array<string, mixed>): array<string, mixed>)|array<model-property<TModel>, mixed>  $attributes
     * @return TModel
     */
    public function makeOne($attributes = []);

    /**
     * Create a collection of models.
     *
     * @param  (callable(array<string, mixed>): array<string, mixed>)|array<model-property<TModel>, mixed>  $attributes
     * @param  \Illuminate\Database\Eloquent\Model|null  $parent
     * @return \Illuminate\Database\Eloquent\Collection<int, TModel>|TModel
     */
    public function make($attributes = [], ?\Illuminate\Database\Eloquent\Model $parent = null);

    /**
     * Make an instance of the model with the given attributes.
     *
     * @param  \Illuminate\Database\Eloquent\Model|null  $parent
     * @return TModel
     */
    protected function makeInstance(?\Illuminate\Database\Eloquent\Model $parent);

    /**
     * Define the model's default state.
     *
     * @return array<model-property<TModel>, mixed>
     */
    abstract public function definition();

    /**
     * Add a new "after making" callback to the model definition.
     *
     * @param  \Closure(TModel): mixed  $callback
     * @return static
     */
    public function afterMaking(\Closure $callback);

    /**
     * Add a new "after creating" callback to the model definition.
     *
     * @param  \Closure(TModel): mixed  $callback
     * @return static
     */
    public function afterCreating(\Closure $callback);
}
