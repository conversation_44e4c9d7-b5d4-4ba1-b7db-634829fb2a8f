<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/**
 * 用户字段映射测试
 *
 * 验证User模型的字段映射是否正确，特别是跨库访问时的字段处理
 */
class UserFieldMappingTest extends TestCase
{
    use RefreshDatabase;

    /**
     * 测试User模型的name访问器
     */
    public function test_user_name_accessor_works_correctly(): void
    {
        // 创建一个测试用户（注意：这里使用的是测试数据库，不是跨库）
        $user = new User;
        $user->username = 'testuser';
        $user->realname = 'Test User';

        // 测试name访问器返回realname
        $this->assertEquals('Test User', $user->name);

        // 测试当realname为空时返回username
        $user->realname = '';
        $this->assertEquals('testuser', $user->name);

        // 测试当realname为null时返回username
        $user->realname = null;
        $this->assertEquals('testuser', $user->name);
    }

    /**
     * 测试User模型的状态检查方法
     */
    public function test_user_status_methods(): void
    {
        $user = new User;

        // 测试isActive方法
        $user->disabled = 1; // 启用
        $this->assertTrue($user->isActive());

        $user->disabled = 2; // 禁用
        $this->assertFalse($user->isActive());

        // 测试isLeader方法
        $user->is_leader = 1; // 是领导
        $this->assertTrue($user->isLeader());

        $user->is_leader = 0; // 不是领导
        $this->assertFalse($user->isLeader());
    }

    /**
     * 测试User模型的查找方法
     */
    public function test_user_find_methods(): void
    {
        // 注意：这些是静态方法测试，在实际环境中会查询跨库数据

        // 测试findByUsername方法存在
        $this->assertTrue(method_exists(User::class, 'findByUsername'));

        // 测试findByEmail方法存在
        $this->assertTrue(method_exists(User::class, 'findByEmail'));

        // 测试findByIdentifier方法存在
        $this->assertTrue(method_exists(User::class, 'findByIdentifier'));
    }

    /**
     * 测试User模型的作用域方法
     */
    public function test_user_scope_methods(): void
    {
        // 测试active作用域
        $query = User::active();
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Builder::class, $query);

        // 测试leaders作用域
        $query = User::leaders();
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Builder::class, $query);

        // 测试byDepartment作用域
        $query = User::byDepartment('test_dept');
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Builder::class, $query);
    }

    /**
     * 测试User模型的数据库连接配置
     */
    public function test_user_database_connection(): void
    {
        $user = new User;

        // 验证使用的是user_system连接
        $this->assertEquals('user_system', $user->getConnectionName());

        // 验证表名
        $this->assertEquals('crs_system_user', $user->getTable());

        // 验证主键
        $this->assertEquals('id', $user->getKeyName());

        // 验证不使用时间戳
        $this->assertFalse($user->usesTimestamps());
    }
}
