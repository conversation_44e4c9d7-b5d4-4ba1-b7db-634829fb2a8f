<?php

namespace Tests\Feature\Auth;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/**
 * 临时认证功能测试 - Cookie认证版本
 */
class TempAuthTest extends TestCase
{
    use RefreshDatabase;

    /**
     * 测试临时登录接口在非生产环境下可用
     */
    public function test_temp_login_available_in_non_production(): void
    {
        // 确保不是生产环境
        $this->app['env'] = 'testing';

        $response = $this->postJson('/api/auth/temp-login', [
            'identifier' => 'nonexistent_user',
        ]);

        // 应该返回用户不存在的错误，而不是功能禁用的错误
        $response->assertStatus(404);
        $response->assertJsonStructure([
            'success',
            'message',
            'code',
        ]);

        // 检查响应中不包含token（因为使用Cookie）
        $response->assertJsonMissing(['token']);
    }

    /**
     * 测试临时登录接口在生产环境下被禁用
     */
    public function test_temp_login_disabled_in_production(): void
    {
        // 模拟生产环境
        $this->app['env'] = 'production';

        $response = $this->postJson('/api/auth/temp-login', [
            'identifier' => 'test_user',
        ]);

        // 应该返回功能禁用的错误
        $response->assertStatus(403);
        $response->assertJsonStructure([
            'success',
            'message',
            'code',
        ]);
    }

    /**
     * 测试获取用户信息接口需要认证
     */
    public function test_profile_requires_authentication(): void
    {
        $response = $this->getJson('/api/auth/profile');

        $response->assertStatus(401);
    }

    /**
     * 测试登出接口需要认证
     */
    public function test_logout_requires_authentication(): void
    {
        $response = $this->postJson('/api/auth/logout');

        $response->assertStatus(401);
    }

    /**
     * 测试临时登录请求验证
     */
    public function test_temp_login_request_validation(): void
    {
        // 确保不是生产环境
        $this->app['env'] = 'testing';

        // 测试缺少identifier参数
        $response = $this->postJson('/api/auth/temp-login', []);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['identifier']);

        // 测试identifier参数为空
        $response = $this->postJson('/api/auth/temp-login', [
            'identifier' => '',
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['identifier']);

        // 测试identifier参数过长
        $response = $this->postJson('/api/auth/temp-login', [
            'identifier' => str_repeat('a', 151),
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['identifier']);
    }
}
