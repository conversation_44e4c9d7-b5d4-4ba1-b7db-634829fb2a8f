<?php

namespace App\DTOs\Auth;

use App\DTOs\BaseDTO;
use App\Models\User;

/**
 * 认证结果数据传输对象
 *
 * 封装用户认证成功后的结果数据
 */
class AuthResultDTO extends BaseDTO
{
    /**
     * 用户对象
     */
    public readonly User $user;

    /**
     * 访问令牌
     */
    public readonly string $token;

    /**
     * 令牌类型
     */
    public readonly string $tokenType;

    /**
     * 令牌过期时间（分钟）
     */
    public readonly ?int $expiresIn;

    /**
     * 构造函数
     *
     * @param  User  $user  用户对象
     * @param  string  $token  访问令牌
     * @param  string  $tokenType  令牌类型
     * @param  int|null  $expiresIn  令牌过期时间（分钟）
     */
    public function __construct(
        User $user,
        string $token,
        string $tokenType = 'Bearer',
        ?int $expiresIn = null
    ) {
        $this->user = $user;
        $this->token = $token;
        $this->tokenType = $tokenType;
        $this->expiresIn = $expiresIn;
    }

    /**
     * 转换为数组
     *
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        $result = [
            'user' => [
                'id' => $this->user->id,
                'username' => $this->user->username,
                'name' => $this->user->realname ?: $this->user->username,
                'realname' => $this->user->realname,
                'email' => $this->user->email,
                'profession' => $this->user->profession,
                'department_id' => $this->user->dept_id,
                'is_leader' => $this->user->isLeader(),
                'phone' => $this->user->phone,
                'is_active' => $this->user->isActive(),
            ],
            'token_type' => $this->tokenType,
            'expires_in' => $this->expiresIn,
        ];

        // 如果是Cookie类型，不返回token（因为已设置在Cookie中）
        if ($this->tokenType !== 'Cookie') {
            $result['token'] = $this->token;
        }

        return $result;
    }
}
