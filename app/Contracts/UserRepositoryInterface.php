<?php

namespace App\Contracts;

use App\Models\User;
use Illuminate\Database\Eloquent\Collection;

/**
 * 用户仓储接口
 *
 * 定义用户数据访问的标准接口，支持跨库访问
 */
interface UserRepositoryInterface
{
    /**
     * 根据ID查找用户
     *
     * @param  int  $id  用户ID
     * @return User|null 用户对象或null
     */
    public function findById(int $id): ?User;

    /**
     * 根据用户名查找用户
     *
     * @param  string  $username  用户名
     * @return User|null 用户对象或null
     */
    public function findByUsername(string $username): ?User;

    /**
     * 根据邮箱查找用户
     *
     * @param  string  $email  邮箱地址
     * @return User|null 用户对象或null
     */
    public function findByEmail(string $email): ?User;

    /**
     * 根据标识符查找用户（用户名或邮箱）
     *
     * @param  string  $identifier  用户标识符（用户名或邮箱）
     * @return User|null 用户对象或null
     */
    public function findByIdentifier(string $identifier): ?User;

    /**
     * 检查用户是否存在且活跃
     *
     * @param  string  $identifier  用户标识符（用户名或邮箱）
     * @return bool 用户是否存在且活跃
     */
    public function existsAndActive(string $identifier): bool;

    /**
     * 获取活跃用户列表
     *
     * @return Collection<User> 活跃用户集合
     */
    public function getActiveUsers(): Collection;

    /**
     * 根据部门获取用户
     *
     * @param  string  $deptId  部门ID
     * @return Collection<User> 部门用户集合
     */
    public function getUsersByDepartment(string $deptId): Collection;

    /**
     * 更新用户最后登录信息
     *
     * @param  int  $userId  用户ID
     * @param  string  $ip  登录IP地址
     * @return bool 更新是否成功
     */
    public function updateLastLogin(int $userId, string $ip): bool;

    /**
     * 更新用户token信息
     *
     * @param  int  $userId  用户ID
     * @param  string  $token  新的token
     * @param  \DateTime|null  $expireAt  token过期时间
     * @return bool 更新是否成功
     */
    public function updateToken(int $userId, string $token, ?\DateTime $expireAt = null): bool;

    /**
     * 清除用户token
     *
     * @param  int  $userId  用户ID
     * @return bool 清除是否成功
     */
    public function clearToken(int $userId): bool;
}
