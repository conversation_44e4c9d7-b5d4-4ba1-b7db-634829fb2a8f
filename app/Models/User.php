<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

/**
 * 用户模型 - 跨库访问版本
 *
 * 访问 crs_system_user 数据库中的用户表
 *
 * @property int $id 用户ID
 * @property string $username 用户名
 * @property string $password 密码
 * @property string $realname 真实姓名
 * @property string $email 邮箱
 * @property string $lastloginip 最后登录IP
 * @property int $lastlogintime 最后登录时间
 * @property int $disabled 是否禁用 1=启用 2=禁用
 * @property int $inputtime 添加时间
 * @property string $profession 职位
 * @property string $dept_id 部门ID
 * @property int $is_leader 是否为leader
 * @property string $phone 手机号
 * @property string $token 系统token
 * @property \DateTime|null $token_expire_at token过期时间
 * @property string $fs_user_id 飞书用户ID
 */
class User extends Authenticatable
{
    use HasFactory, Notifiable;

    /**
     * 指定数据库连接
     */
    protected $connection = 'user_system';

    /**
     * 指定表名
     */
    protected $table = 'crs_system_user';

    /**
     * 主键字段
     */
    protected $primaryKey = 'id';

    /**
     * 是否使用时间戳
     */
    public $timestamps = false;

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'username',
        'password',
        'realname',
        'email',
        'profession',
        'dept_id',
        'is_leader',
        'phone',
        'disabled',
        'fs_user_id',
    ];

    /**
     * 隐藏的属性
     */
    protected $hidden = [
        'password',
        'token',
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'lastlogintime' => 'timestamp',
        'inputtime' => 'timestamp',
        'token_expire_at' => 'datetime',
        'disabled' => 'integer',
        'is_leader' => 'boolean',
    ];

    /**
     * 获取认证标识符名称
     */
    public function getAuthIdentifierName(): string
    {
        return 'id';
    }

    /**
     * 获取认证标识符
     */
    public function getAuthIdentifier()
    {
        return $this->getKey();
    }

    /**
     * 获取认证密码
     */
    public function getAuthPassword(): string
    {
        return $this->password;
    }

    /**
     * 获取记住我令牌名称
     */
    public function getRememberTokenName(): string
    {
        return 'remember_token'; // 注意：原表没有此字段，需要处理
    }

    /**
     * 获取记住我令牌
     */
    public function getRememberToken()
    {
        return null; // 原表没有remember_token字段
    }

    /**
     * 设置记住我令牌
     */
    public function setRememberToken($value): void
    {
        // 原表没有remember_token字段，不做处理
    }

    /**
     * 用户名属性访问器（兼容Laravel标准）
     */
    protected function name(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->realname ?: $this->username,
        );
    }

    /**
     * 检查用户是否启用
     */
    public function isActive(): bool
    {
        return $this->disabled === 1;
    }

    /**
     * 检查用户是否为领导
     */
    public function isLeader(): bool
    {
        return $this->is_leader === 1;
    }

    /**
     * 根据用户名查找用户
     */
    public static function findByUsername(string $username): ?self
    {
        return static::where('username', $username)->first();
    }

    /**
     * 根据邮箱查找用户
     */
    public static function findByEmail(string $email): ?self
    {
        return static::where('email', $email)->first();
    }

    /**
     * 根据标识符查找用户（支持用户名或邮箱）
     */
    public static function findByIdentifier(string $identifier): ?self
    {
        return static::where('username', $identifier)
            ->orWhere('email', $identifier)
            ->first();
    }

    /**
     * 获取活跃用户
     */
    public function scopeActive(\Illuminate\Database\Eloquent\Builder $query): \Illuminate\Database\Eloquent\Builder
    {
        return $query->where('disabled', 1);
    }

    /**
     * 获取领导用户
     */
    public function scopeLeaders(\Illuminate\Database\Eloquent\Builder $query): \Illuminate\Database\Eloquent\Builder
    {
        return $query->where('is_leader', 1);
    }

    /**
     * 按部门筛选
     */
    public function scopeByDepartment(\Illuminate\Database\Eloquent\Builder $query, string $deptId): \Illuminate\Database\Eloquent\Builder
    {
        return $query->where('dept_id', $deptId);
    }
}
