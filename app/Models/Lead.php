<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 线索模型
 *
 * @property int $id
 * @property string $company_full_name 公司全称
 * @property string|null $company_short_name 公司简称
 * @property string|null $internal_name 内部统称
 * @property int|null $region 所属区域
 * @property int|null $source 线索来源
 * @property int|null $industry 所属行业
 * @property int|null $status 线索状态
 * @property int|null $stage 线索阶段
 * @property string|null $address 详细地址
 * @property int|null $creator_id 创建人用户ID
 * @property Carbon|null $last_followed_at 最近跟进时间
 * @property string|null $remark 备注
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon|null $deleted_at
 * @property-read string $status_label 状态标签
 * @property-read string $region_label 区域标签
 * @property-read string $source_label 来源标签
 * @property-read string $industry_label 行业标签
 * @property-read string $stage_label 阶段标签
 */
class Lead extends Model
{
    use HasFactory, SoftDeletes;

    // 线索状态映射
    public const STATUS_LABELS = [
        1 => '待跟进',
        2 => '跟进中',
        3 => '已转化',
        4 => '无意向',
    ];

    // 所属区域映射
    public const REGION_LABELS = [
        1 => '华北',
        2 => '东北',
        3 => '华东',
        4 => '华南',
        5 => '西南',
        6 => '西北',
        7 => '华中',
    ];

    // 线索来源映射
    public const SOURCE_LABELS = [
        1 => '官网',
        2 => '电话',
        3 => '邮件',
        4 => '展会',
        5 => '推荐',
        6 => '广告',
        7 => '社交媒体',
        8 => '其他',
    ];

    // 所属行业映射
    public const INDUSTRY_LABELS = [
        1 => '科技',
        2 => '金融',
        3 => '制造业',
        4 => '零售',
        5 => '医疗',
        6 => '教育',
        7 => '房地产',
        8 => '物流',
        9 => '能源',
        10 => '农业',
        11 => '娱乐',
        12 => '其他',
    ];

    // 线索阶段映射
    public const STAGE_LABELS = [
        1 => '初始接触',
        2 => '产生兴趣',
        3 => '考虑阶段',
        4 => '购买意向',
        5 => '评估阶段',
        6 => '购买阶段',
    ];

    /**
     * 数据表名称
     *
     * @var string
     */
    protected $table = 'crm_lead';

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_full_name',
        'company_short_name',
        'internal_name',
        'region',
        'source',
        'industry',
        'status',
        'stage',
        'address',
        'creator_id',
        'last_followed_at',
        'remark',
    ];

    /**
     * 序列化时自动附带的属性
     *
     * @var array<int, string>
     */
    protected $appends = [
        'status_label',
        'region_label',
        'source_label',
        'industry_label',
        'stage_label',
    ];

    /**
     * 属性类型转换
     *
     * @var array<string, string>
     */
    protected $casts = [
        'region' => 'integer',
        'source' => 'integer',
        'industry' => 'integer',
        'status' => 'integer',
        'stage' => 'integer',
        'creator_id' => 'integer',
        'last_followed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * 检查状态是否允许删除
     *
     * @param  int  $status  状态值
     */
    public static function isDeletableStatus(int $status): bool
    {
        return ! in_array($status, self::getNonDeletableStatuses());
    }

    /**
     * 获取不允许删除的状态列表
     */
    public static function getNonDeletableStatuses(): array
    {
        return config('business.Lead.non_deletable_statuses', [3, 4]);
    }

    /**
     * 创建人关联（跨库安全版本）
     *
     * 注意：由于跨数据库限制，不能直接使用Eloquent关联查询
     * 请使用CrossDatabaseRelationService来加载关联数据
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    /**
     * 关联的用户（多对多，跨库安全版本）
     *
     * 注意：由于跨数据库限制，不能直接使用Eloquent关联查询
     * 请使用CrossDatabaseRelationService来加载关联数据
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'crm_lead_user_relation', 'lead_id', 'user_id')
            ->withPivot(['role_type', 'is_primary']);
    }

    /**
     * 获取创建人信息（跨库安全方法）
     */
    public function getCreatorAttribute(): ?User
    {
        if (! $this->relationLoaded('creator') && $this->creator_id) {
            $creator = User::find($this->creator_id);
            $this->setRelation('creator', $creator);
        }

        return $this->getRelation('creator');
    }

    /**
     * 获取关联用户信息（跨库安全方法）
     */
    public function getUsersAttribute(): \Illuminate\Database\Eloquent\Collection
    {
        if (! $this->relationLoaded('users')) {
            $crossDbService = app(\App\Services\CrossDatabaseRelationService::class);
            $crossDbService->loadUsersForLead($this);
        }

        return $this->getRelation('users');
    }

    /**
     * 关联的联系人（多对多）
     */
    public function contacts(): BelongsToMany
    {
        return $this->belongsToMany(Contact::class, 'crm_lead_contact_relation', 'lead_id', 'contact_id');
    }

    /**
     * 获取状态标签
     */
    public function getStatusLabelAttribute(): string
    {
        return static::STATUS_LABELS[$this->status] ?? '';
    }

    /**
     * 获取区域标签
     */
    public function getRegionLabelAttribute(): string
    {
        return static::REGION_LABELS[$this->region] ?? '';
    }

    /**
     * 获取来源标签
     */
    public function getSourceLabelAttribute(): string
    {
        return static::SOURCE_LABELS[$this->source] ?? '';
    }

    /**
     * 获取行业标签
     */
    public function getIndustryLabelAttribute(): string
    {
        return static::INDUSTRY_LABELS[$this->industry] ?? '';
    }

    /**
     * 获取阶段标签
     *
     * @return string
     */
    //    public function getStageLabelAttribute(): string
    //    {
    //        return static::STAGE_LABELS[$this->stage] ?? '';
    //    }
}
