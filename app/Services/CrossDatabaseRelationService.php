<?php

namespace App\Services;

use App\Models\Lead;
use App\Models\LeadUserRelation;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use stdClass;

/**
 * 跨数据库关联服务
 *
 * 处理跨数据库的模型关联查询，避免直接的数据库JOIN操作
 */
class CrossDatabaseRelationService
{
    /**
     * 为线索集合加载创建人信息
     *
     * @param  Collection  $leads  线索集合
     * @return Collection 加载了创建人信息的线索集合
     */
    public function loadCreatorsForLeads(Collection $leads): Collection
    {
        if ($leads->isEmpty()) {
            return $leads;
        }

        // 获取所有创建人ID
        $creatorIds = $leads->pluck('creator_id')->filter()->unique()->values();

        if ($creatorIds->isEmpty()) {
            return $leads;
        }

        // 分离查询：从用户系统数据库查询创建人信息
        $creators = User::whereIn('id', $creatorIds->toArray())
            ->select(['id', 'username', 'realname', 'email'])
            ->get()
            ->keyBy('id');

        // 在应用层组合数据
        foreach ($leads as $lead) {
            if ($lead instanceof Lead && $lead->creator_id && $creators->has($lead->creator_id)) {
                $lead->setRelation('creator', $creators->get($lead->creator_id));
            }
        }

        return $leads;
    }

    /**
     * 为线索集合加载关联用户信息
     *
     * @param  Collection  $leads  线索集合
     * @return Collection 加载了关联用户信息的线索集合
     */
    public function loadUsersForLeads(Collection $leads): Collection
    {
        if ($leads->isEmpty()) {
            return $leads;
        }

        $leadIds = $leads->pluck('id')->toArray();

        // 第一步：查询线索用户关联关系
        $relations = LeadUserRelation::whereIn('lead_id', $leadIds)
            ->select(['lead_id', 'user_id', 'role_type', 'is_primary'])
            ->get();

        if ($relations->isEmpty()) {
            // 如果没有关联关系，为每个线索设置空集合
            foreach ($leads as $lead) {
                if ($lead instanceof Lead) {
                    $lead->setRelation('users', new Collection);
                }
            }

            return $leads;
        }

        // 第二步：获取所有用户ID
        $userIds = $relations->pluck('user_id')->unique()->values()->toArray();

        // 第三步：分离查询用户信息
        $users = User::whereIn('id', $userIds)
            ->select(['id', 'username', 'realname', 'email'])
            ->get()
            ->keyBy('id');

        // 第四步：在应用层组合数据
        $leadUserMap = $relations->groupBy('lead_id');

        foreach ($leads as $lead) {
            if (! ($lead instanceof Lead)) {
                continue;
            }

            $leadRelations = $leadUserMap->get($lead->id, collect());

            $leadUsers = $leadRelations->map(function ($relation) use ($users) {
                $user = $users->get($relation->user_id);
                if ($user) {
                    // 添加pivot数据
                    $pivotData = new stdClass;
                    $pivotData->lead_id = $relation->lead_id;
                    $pivotData->user_id = $relation->user_id;
                    $pivotData->role_type = $relation->role_type;
                    $pivotData->is_primary = $relation->is_primary;
                    $user->setAttribute('pivot', $pivotData);
                }

                return $user;
            })->filter();

            $lead->setRelation('users', new Collection($leadUsers->toArray()));
        }

        return $leads;
    }

    /**
     * 为单个线索加载创建人信息
     *
     * @param  Lead  $lead  线索对象
     * @return Lead 加载了创建人信息的线索对象
     */
    public function loadCreatorForLead(Lead $lead): Lead
    {
        if (! $lead->creator_id) {
            return $lead;
        }

        $creator = User::find($lead->creator_id);
        if ($creator) {
            $lead->setRelation('creator', $creator);
        }

        return $lead;
    }

    /**
     * 为单个线索加载关联用户信息
     *
     * @param  Lead  $lead  线索对象
     * @return Lead 加载了关联用户信息的线索对象
     */
    public function loadUsersForLead(Lead $lead): Lead
    {
        // 查询线索用户关联关系
        $relations = LeadUserRelation::where('lead_id', $lead->id)
            ->select(['user_id', 'role_type', 'is_primary'])
            ->get();

        if ($relations->isEmpty()) {
            $lead->setRelation('users', new Collection);

            return $lead;
        }

        // 获取用户信息
        $userIds = $relations->pluck('user_id')->toArray();
        $users = User::whereIn('id', $userIds)
            ->select(['id', 'username', 'realname', 'email'])
            ->get()
            ->keyBy('id');

        // 组合数据
        $leadUsers = $relations->map(function ($relation) use ($users, $lead) {
            $user = $users->get($relation->user_id);
            if ($user) {
                // 添加pivot数据
                $pivotData = new stdClass;
                $pivotData->lead_id = $lead->id;
                $pivotData->user_id = $relation->user_id;
                $pivotData->role_type = $relation->role_type;
                $pivotData->is_primary = $relation->is_primary;
                $user->setAttribute('pivot', $pivotData);
            }

            return $user;
        })->filter();

        $lead->setRelation('users', new Collection($leadUsers->toArray()));

        return $lead;
    }

    /**
     * 获取线索的主负责人
     *
     * @param  int  $leadId  线索ID
     * @return User|null 主负责人用户对象
     */
    public function getPrimaryOwnerForLead(int $leadId): ?User
    {
        $relation = LeadUserRelation::where('lead_id', $leadId)
            ->where('role_type', 1) // ROLE_TYPE_OWNER
            ->where('is_primary', 1)
            ->first();

        if (! $relation) {
            return null;
        }

        return User::find($relation->user_id);
    }

    /**
     * 获取线索的协作者
     *
     * @param  int  $leadId  线索ID
     * @return Collection<User> 协作者用户集合
     */
    public function getCollaboratorsForLead(int $leadId): Collection
    {
        $relations = LeadUserRelation::where('lead_id', $leadId)
            ->where('role_type', 2) // ROLE_TYPE_COLLABORATOR
            ->get();

        if ($relations->isEmpty()) {
            return new Collection;
        }

        $userIds = $relations->pluck('user_id')->toArray();
        $users = User::whereIn('id', $userIds)
            ->select(['id', 'username', 'realname', 'email'])
            ->get();

        // 添加pivot数据
        $users->each(function ($user) use ($relations, $leadId) {
            $relation = $relations->firstWhere('user_id', $user->id);
            if ($relation) {
                $pivotData = new stdClass;
                $pivotData->lead_id = $leadId;
                $pivotData->user_id = $user->id;
                $pivotData->role_type = $relation->role_type;
                $pivotData->is_primary = $relation->is_primary;
                $user->setAttribute('pivot', $pivotData);
            }
        });

        return $users;
    }
}
