<?php

namespace App\Services;

use App\Contracts\UserRepositoryInterface;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;

/**
 * Cookie认证服务
 *
 * 处理基于Cookie的用户认证逻辑，支持SSO集成
 */
class CookieAuthService
{
    /**
     * Cookie名称
     */
    private const COOKIE_NAME = 'crm_auth_token';

    /**
     * 用户仓储
     */
    private UserRepositoryInterface $userRepository;

    /**
     * 构造函数
     *
     * @param  UserRepositoryInterface  $userRepository  用户仓储接口
     */
    public function __construct(UserRepositoryInterface $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    /**
     * 生成认证token
     *
     * @param  User  $user  用户对象
     * @param  int  $expiresIn  过期时间（分钟）
     * @return string 加密的token
     */
    public function generateToken(User $user, int $expiresIn = 480): string
    {
        $payload = [
            'user_id' => $user->id,
            'username' => $user->username,
            'expires_at' => now()->addMinutes($expiresIn)->timestamp,
            'issued_at' => now()->timestamp,
        ];

        return Crypt::encrypt($payload);
    }

    /**
     * 验证token并获取用户
     *
     * @param  string  $token  加密的token
     * @return User|null 用户对象或null
     */
    public function validateToken(string $token): ?User
    {
        try {
            $payload = Crypt::decrypt($token);

            // 检查token格式
            if (! is_array($payload) || ! isset($payload['user_id'], $payload['expires_at'])) {
                return null;
            }

            // 检查token是否过期
            if ($payload['expires_at'] < now()->timestamp) {
                return null;
            }

            // 获取用户信息
            $user = $this->userRepository->findById($payload['user_id']);

            // 检查用户是否存在且活跃
            if (! $user || ! $user->isActive()) {
                return null;
            }

            return $user;
        } catch (\Exception $e) {
            Log::warning('Token验证失败', [
                'error' => $e->getMessage(),
                'token' => substr($token, 0, 20).'...',
            ]);

            return null;
        }
    }

    /**
     * 从请求中获取token
     *
     * @param  Request  $request  HTTP请求
     * @return string|null token或null
     */
    public function getTokenFromRequest(Request $request): ?string
    {
        $token = $request->cookie(self::COOKIE_NAME);

        return is_string($token) ? $token : null;
    }

    /**
     * 设置认证cookie
     *
     * @param  \Illuminate\Http\JsonResponse|\Illuminate\Http\Response  $response  HTTP响应
     * @param  string  $token  认证token
     * @param  int  $expiresIn  过期时间（分钟）
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\Response 设置了cookie的响应
     */
    public function setAuthCookie($response, string $token, int $expiresIn = 480)
    {
        $cookie = cookie(
            name: self::COOKIE_NAME,
            value: $token,
            minutes: $expiresIn,
            path: '/',
            domain: null,
            secure: app()->environment('production'), // 生产环境使用HTTPS
            httpOnly: true, // 防止XSS攻击
            raw: false,
            sameSite: 'Lax' // CSRF保护
        );

        return $response->withCookie($cookie);
    }

    /**
     * 清除认证cookie
     *
     * @param  \Illuminate\Http\JsonResponse|\Illuminate\Http\Response  $response  HTTP响应
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\Response 清除了cookie的响应
     */
    public function clearAuthCookie($response)
    {
        $cookie = cookie()->forget(self::COOKIE_NAME);

        return $response->withCookie($cookie);
    }

    /**
     * 刷新token（延长有效期）
     *
     * @param  string  $token  当前token
     * @param  int  $expiresIn  新的过期时间（分钟）
     * @return string|null 新的token或null
     */
    public function refreshToken(string $token, int $expiresIn = 480): ?string
    {
        $user = $this->validateToken($token);

        if (! $user) {
            return null;
        }

        return $this->generateToken($user, $expiresIn);
    }

    /**
     * 获取token信息
     *
     * @param  string  $token  加密的token
     * @return array{user_id: int|null, username: string|null, expires_at: int|null, issued_at: int|null, is_expired: bool}|null token信息或null
     */
    public function getTokenInfo(string $token): ?array
    {
        try {
            $payload = Crypt::decrypt($token);

            if (! is_array($payload)) {
                return null;
            }

            return [
                'user_id' => $payload['user_id'] ?? null,
                'username' => $payload['username'] ?? null,
                'expires_at' => $payload['expires_at'] ?? null,
                'issued_at' => $payload['issued_at'] ?? null,
                'is_expired' => ($payload['expires_at'] ?? 0) < now()->timestamp,
            ];
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 获取Cookie名称
     *
     * @return string Cookie名称
     */
    public function getCookieName(): string
    {
        return self::COOKIE_NAME;
    }
}
