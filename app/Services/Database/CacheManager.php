<?php

namespace App\Services\Database;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

/**
 * 数据库查询缓存管理器
 *
 * 实现多层次缓存策略，提供智能缓存键生成、缓存失效和性能监控功能
 */
class CacheManager
{
    /**
     * 内存缓存存储
     *
     * @var array<string, array>
     */
    protected static array $memoryCache = [];

    /**
     * 内存缓存访问时间记录
     *
     * @var array<string, float>
     */
    protected static array $accessTimes = [];

    /**
     * 内存缓存最大容量
     */
    protected int $maxMemoryCacheSize = 1000;

    /**
     * 缓存性能指标
     *
     * @var array<string, mixed>
     */
    protected array $metrics = [
        'hits' => 0,
        'misses' => 0,
        'total_queries' => 0,
        'memory_hits' => 0,
        'redis_hits' => 0,
    ];

    /**
     * 表与缓存键的映射关系
     *
     * @var array<string, array<string>>
     */
    protected array $tableKeyMappings = [
        'crm_lead' => ['lead_*', 'leads_*', 'lead_stats_*'],
        'crm_contact' => ['contact_*', 'contacts_*'],
        'crm_lead_contact_relation' => ['lead_*', 'contact_*'],
        'crm_lead_user_relation' => ['lead_*', 'user_leads_*'],
    ];

    /**
     * 生成智能缓存键
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  array<string, mixed>  $context  额外上下文
     */
    public function generateCacheKey(Builder|QueryBuilder $query, array $context = []): string
    {
        // 获取 SQL 和绑定参数
        $sql = $query->toSql();
        $bindings = $query->getBindings();

        // 标准化 SQL
        $normalizedSql = $this->normalizeSql($sql);

        // 生成参数签名
        $bindingsSignature = $this->generateBindingsSignature($bindings);

        // 获取查询上下文
        $queryContext = $this->getQueryContext($context);

        // 生成最终缓存键
        return sprintf(
            'query_cache:%s:%s:%s',
            substr(md5($normalizedSql), 0, 12),
            $bindingsSignature,
            $queryContext
        );
    }

    /**
     * 获取缓存数据（多层缓存策略）
     *
     * @param  string  $cacheKey  缓存键
     * @return mixed|null
     */
    public function get(string $cacheKey): mixed
    {
        $startTime = microtime(true);

        // L1: 检查内存缓存
        $memoryResult = $this->getFromMemoryCache($cacheKey);
        if ($memoryResult !== null) {
            $this->recordCacheHit($cacheKey, microtime(true) - $startTime, 'memory');

            return $memoryResult;
        }

        // L2: 检查 Redis 缓存
        $redisResult = Cache::get($cacheKey);
        if ($redisResult !== null) {
            // 将结果存入内存缓存
            $this->setToMemoryCache($cacheKey, $redisResult, 300);
            $this->recordCacheHit($cacheKey, microtime(true) - $startTime, 'redis');

            return $redisResult;
        }

        // 缓存未命中
        $this->recordCacheMiss($cacheKey, microtime(true) - $startTime);

        return null;
    }

    /**
     * 设置缓存数据
     *
     * @param  string  $cacheKey  缓存键
     * @param  mixed  $data  缓存数据
     * @param  int  $ttl  过期时间（秒）
     */
    public function set(string $cacheKey, mixed $data, int $ttl = 3600): bool
    {
        try {
            // 计算自适应 TTL
            $adaptiveTtl = $this->calculateAdaptiveTtl($data, $ttl);

            // 存储到 Redis
            $redisResult = Cache::put($cacheKey, $data, $adaptiveTtl);

            // 存储到内存缓存（较短的 TTL）
            $memoryTtl = min($adaptiveTtl, 300);
            $this->setToMemoryCache($cacheKey, $data, $memoryTtl);

            // 记录缓存统计
            $this->recordCacheSet($cacheKey, $adaptiveTtl);

            return $redisResult;

        } catch (\Exception $e) {
            Log::error('缓存设置失败', [
                'cache_key' => $cacheKey,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 删除缓存
     *
     * @param  string|array<string>  $cacheKeys  缓存键或缓存键数组
     * @return int 删除的缓存数量
     */
    public function forget(string|array $cacheKeys): int
    {
        if (is_string($cacheKeys)) {
            $cacheKeys = [$cacheKeys];
        }

        $deletedCount = 0;

        foreach ($cacheKeys as $key) {
            try {
                // 从 Redis 删除
                if (Cache::forget($key)) {
                    $deletedCount++;
                }

                // 从内存缓存删除
                $this->forgetFromMemoryCache($key);

            } catch (\Exception $e) {
                Log::warning('缓存删除失败', [
                    'cache_key' => $key,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $deletedCount;
    }

    /**
     * 根据模式删除缓存
     *
     * @param  string  $pattern  缓存键模式
     * @return int 删除的缓存数量
     */
    public function forgetByPattern(string $pattern): int
    {
        try {
            $keys = $this->findKeysByPattern($pattern);

            return $this->forget($keys);

        } catch (\Exception $e) {
            Log::error('按模式删除缓存失败', [
                'pattern' => $pattern,
                'error' => $e->getMessage(),
            ]);

            return 0;
        }
    }

    /**
     * 根据表名失效相关缓存
     *
     * @param  string  $tableName  表名
     * @return int 失效的缓存数量
     */
    public function invalidateByTable(string $tableName): int
    {
        $patterns = $this->tableKeyMappings[$tableName] ?? [];
        $totalInvalidated = 0;

        foreach ($patterns as $pattern) {
            $invalidated = $this->forgetByPattern($pattern);
            $totalInvalidated += $invalidated;
        }

        if ($totalInvalidated > 0) {
            Log::info('表相关缓存失效完成', [
                'table' => $tableName,
                'invalidated_count' => $totalInvalidated,
            ]);
        }

        return $totalInvalidated;
    }

    /**
     * 获取缓存统计信息
     *
     * @return array<string, mixed>
     */
    public function getStatistics(): array
    {
        $hitRate = $this->metrics['total_queries'] > 0
            ? ($this->metrics['hits'] / $this->metrics['total_queries']) * 100
            : 0;

        return [
            'total_queries' => $this->metrics['total_queries'],
            'cache_hits' => $this->metrics['hits'],
            'cache_misses' => $this->metrics['misses'],
            'hit_rate' => round($hitRate, 2),
            'memory_hits' => $this->metrics['memory_hits'],
            'redis_hits' => $this->metrics['redis_hits'],
            'memory_cache_size' => count(self::$memoryCache),
            'memory_cache_limit' => $this->maxMemoryCacheSize,
        ];
    }

    /**
     * 清理过期的内存缓存
     *
     * @return int 清理的缓存数量
     */
    public function cleanupExpiredMemoryCache(): int
    {
        $currentTime = time();
        $cleanedCount = 0;

        foreach (self::$memoryCache as $key => $item) {
            if ($item['expires_at'] < $currentTime) {
                unset(self::$memoryCache[$key], self::$accessTimes[$key]);
                $cleanedCount++;
            }
        }

        return $cleanedCount;
    }

    /**
     * 标准化 SQL 语句
     *
     * @param  string  $sql  SQL 语句
     */
    protected function normalizeSql(string $sql): string
    {
        // 移除多余空格和换行
        $sql = preg_replace('/\s+/', ' ', $sql);

        // 统一关键字大小写
        $keywords = ['select', 'from', 'where', 'join', 'left', 'right', 'inner',
            'order', 'group', 'having', 'limit', 'offset', 'union'];

        foreach ($keywords as $keyword) {
            $sql = preg_replace('/\b'.$keyword.'\b/i', strtoupper($keyword), $sql);
        }

        return trim($sql);
    }

    /**
     * 生成绑定参数签名
     *
     * @param  array<mixed>  $bindings  绑定参数
     */
    protected function generateBindingsSignature(array $bindings): string
    {
        if (empty($bindings)) {
            return 'no_bindings';
        }

        $typedBindings = array_map(function ($binding) {
            return match (true) {
                is_string($binding) => 'str:'.substr(md5($binding), 0, 8),
                is_numeric($binding) => 'num:'.$binding,
                is_bool($binding) => 'bool:'.($binding ? '1' : '0'),
                is_null($binding) => 'null',
                default => 'obj:'.substr(md5(serialize($binding)), 0, 8)
            };
        }, $bindings);

        return substr(md5(implode('|', $typedBindings)), 0, 8);
    }

    /**
     * 获取查询上下文
     *
     * @param  array<string, mixed>  $context  额外上下文
     */
    protected function getQueryContext(array $context = []): string
    {
        $defaultContext = [
            'user_id' => auth()->id() ?? 'guest',
            'locale' => app()->getLocale(),
            'time_window' => floor(time() / 300) * 300, // 5分钟时间窗口
        ];

        $fullContext = array_merge($defaultContext, $context);

        return substr(md5(serialize($fullContext)), 0, 8);
    }

    /**
     * 从内存缓存获取数据
     *
     * @param  string  $key  缓存键
     * @return mixed|null
     */
    protected function getFromMemoryCache(string $key): mixed
    {
        if (! isset(self::$memoryCache[$key])) {
            return null;
        }

        $item = self::$memoryCache[$key];

        // 检查是否过期
        if ($item['expires_at'] < time()) {
            unset(self::$memoryCache[$key], self::$accessTimes[$key]);

            return null;
        }

        // 更新访问时间
        self::$accessTimes[$key] = microtime(true);

        return $item['data'];
    }

    /**
     * 设置内存缓存数据
     *
     * @param  string  $key  缓存键
     * @param  mixed  $data  缓存数据
     * @param  int  $ttl  过期时间
     */
    protected function setToMemoryCache(string $key, mixed $data, int $ttl): void
    {
        // 检查缓存大小，执行 LRU 清理
        if (count(self::$memoryCache) >= $this->maxMemoryCacheSize) {
            $this->evictLeastRecentlyUsed();
        }

        self::$memoryCache[$key] = [
            'data' => $data,
            'expires_at' => time() + $ttl,
            'created_at' => time(),
        ];

        self::$accessTimes[$key] = microtime(true);
    }

    /**
     * 从内存缓存删除数据
     *
     * @param  string  $key  缓存键
     */
    protected function forgetFromMemoryCache(string $key): void
    {
        unset(self::$memoryCache[$key], self::$accessTimes[$key]);
    }

    /**
     * LRU 清理策略
     */
    protected function evictLeastRecentlyUsed(): void
    {
        if (empty(self::$accessTimes)) {
            return;
        }

        // 排序找出最少使用的键
        asort(self::$accessTimes);
        $lruKeys = array_slice(array_keys(self::$accessTimes), 0, 100);

        foreach ($lruKeys as $key) {
            unset(self::$memoryCache[$key], self::$accessTimes[$key]);
        }
    }

    /**
     * 计算自适应 TTL
     *
     * @param  mixed  $data  缓存数据
     * @param  int  $baseTtl  基础 TTL
     */
    protected function calculateAdaptiveTtl(mixed $data, int $baseTtl): int
    {
        $config = config('database_optimization.query_builder.cache.adaptive_ttl', []);

        if (! ($config['enabled'] ?? false)) {
            return $baseTtl;
        }

        $minTtl = $config['min_ttl'] ?? 300;
        $maxTtl = $config['max_ttl'] ?? 7200;

        // 根据数据大小调整 TTL
        $dataSize = strlen(serialize($data));
        $sizeFactor = min($dataSize / 10240, 2); // 10KB 为基准

        $adaptiveTtl = $baseTtl * (1 + $sizeFactor);

        return max($minTtl, min($maxTtl, (int) $adaptiveTtl));
    }

    /**
     * 根据模式查找缓存键
     *
     * @param  string  $pattern  模式
     * @return array<string>
     */
    protected function findKeysByPattern(string $pattern): array
    {
        try {
            $keys = [];
            $cursor = 0;

            do {
                $result = Redis::scan($cursor, ['match' => $pattern, 'count' => 100]);
                $cursor = $result[0];
                $keys = array_merge($keys, $result[1]);
            } while ($cursor != 0);

            return $keys;

        } catch (\Exception $e) {
            Log::warning('查找缓存键失败', [
                'pattern' => $pattern,
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * 记录缓存命中
     *
     * @param  string  $cacheKey  缓存键
     * @param  float  $responseTime  响应时间
     * @param  string  $layer  缓存层级
     */
    protected function recordCacheHit(string $cacheKey, float $responseTime, string $layer): void
    {
        $this->metrics['hits']++;
        $this->metrics['total_queries']++;
        $this->metrics[$layer.'_hits']++;

        // 定期上报指标
        if ($this->metrics['total_queries'] % 100 === 0) {
            $this->reportMetrics();
        }
    }

    /**
     * 记录缓存未命中
     *
     * @param  string  $cacheKey  缓存键
     * @param  float  $queryTime  查询时间
     */
    protected function recordCacheMiss(string $cacheKey, float $queryTime): void
    {
        $this->metrics['misses']++;
        $this->metrics['total_queries']++;
    }

    /**
     * 记录缓存设置
     *
     * @param  string  $cacheKey  缓存键
     * @param  int  $ttl  TTL
     */
    protected function recordCacheSet(string $cacheKey, int $ttl): void
    {
        // 记录缓存设置统计
    }

    /**
     * 上报性能指标
     */
    protected function reportMetrics(): void
    {
        $stats = $this->getStatistics();

        Log::info('缓存性能指标', $stats);

        // 重置计数器
        $this->metrics = array_map(fn () => 0, $this->metrics);
    }
}
