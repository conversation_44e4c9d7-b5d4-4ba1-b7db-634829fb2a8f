<?php

namespace App\Services;

use App\Contracts\UserRepositoryInterface;
use App\DTOs\Auth\AuthResultDTO;
use App\Exceptions\BusinessException;
use App\Facades\BusinessLog;
use App\Models\User;

/**
 * 临时认证服务 - Cookie认证版本
 *
 * 处理开发测试阶段的用户认证逻辑，基于Cookie存储token
 */
class TempAuthService
{
    /**
     * 用户仓储
     */
    private UserRepositoryInterface $userRepository;

    /**
     * Cookie认证服务
     */
    private CookieAuthService $cookieAuthService;

    /**
     * 构造函数
     *
     * @param  UserRepositoryInterface  $userRepository  用户仓储接口
     * @param  CookieAuthService  $cookieAuthService  Cookie认证服务
     */
    public function __construct(
        UserRepositoryInterface $userRepository,
        CookieAuthService $cookieAuthService
    ) {
        $this->userRepository = $userRepository;
        $this->cookieAuthService = $cookieAuthService;
    }

    /**
     * 临时登录（开发测试用）
     *
     * @param  string  $identifier  用户标识符（用户名或邮箱）
     * @return AuthResultDTO 认证结果
     *
     * @throws BusinessException 当用户不存在或认证失败时
     */
    public function tempLogin(string $identifier): AuthResultDTO
    {
        // 检查是否为生产环境
        if (app()->environment('production')) {
            throw BusinessException::fromErrorCode('Auth.temp_login_disabled');
        }

        // 验证用户存在性和状态
        if (! $this->validateUser($identifier)) {
            throw BusinessException::fromErrorCode('Auth.user_not_found');
        }

        // 查找用户
        $user = $this->userRepository->findByIdentifier($identifier);

        if (! $user) {
            throw BusinessException::fromErrorCode('Auth.user_not_found');
        }

        // 检查用户状态
        if (! $user->isActive()) {
            throw BusinessException::fromErrorCode('Auth.user_inactive');
        }

        // 生成Cookie token
        $expiresIn = config('business.Auth.token.temp_expiration', 480); // 8小时
        $token = $this->cookieAuthService->generateToken($user, $expiresIn);

        // 更新最后登录信息
        $this->userRepository->updateLastLogin(
            $user->id,
            request()->ip() ?? '127.0.0.1'
        );

        // 记录业务日志
        BusinessLog::info([
            'message' => '临时登录成功',
            'module' => 'Auth',
            'action' => 'temp_login',
            'user_id' => $user->id,
            'username' => $user->username,
            'login_type' => 'temp',
            'ip_address' => request()->ip(),
        ]);

        return new AuthResultDTO($user, $token, 'Cookie', $expiresIn);
    }

    /**
     * 验证用户存在性和状态
     *
     * @param  string  $identifier  用户标识符（用户名或邮箱）
     * @return bool 用户是否有效
     */
    public function validateUser(string $identifier): bool
    {
        return $this->userRepository->existsAndActive($identifier);
    }

    /**
     * 生成Cookie token
     *
     * @param  User  $user  用户对象
     * @param  int  $expiresIn  过期时间（分钟）
     * @return string Cookie token
     */
    public function generateToken(User $user, int $expiresIn = 480): string
    {
        return $this->cookieAuthService->generateToken($user, $expiresIn);
    }

    /**
     * 清除用户认证（登出）
     *
     * @param  User  $user  用户对象
     * @return bool 清除是否成功
     */
    public function logout(User $user): bool
    {
        try {
            // 记录业务日志
            BusinessLog::info([
                'message' => '用户登出成功',
                'module' => 'Auth',
                'action' => 'logout',
                'user_id' => $user->id,
                'username' => $user->username,
            ]);

            return true;
        } catch (\Exception $e) {
            BusinessLog::error([
                'message' => '用户登出失败',
                'module' => 'Auth',
                'action' => 'logout',
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 验证密码（如果需要）
     *
     * @param  User  $user  用户对象
     * @param  string  $password  密码
     * @return bool 密码是否正确
     */
    public function verifyPassword(User $user, string $password): bool
    {
        // 注意：原系统使用MD5加密，需要适配
        return md5($password) === $user->password;
    }

    /**
     * SSO登录（预留接口）
     *
     * @param  string  $ssoToken  SSO系统返回的令牌
     * @return AuthResultDTO 认证结果
     *
     * @throws \BadMethodCallException 当前未实现
     */
    public function ssoLogin(string $ssoToken): AuthResultDTO
    {
        throw new \BadMethodCallException('SSO login not implemented yet');
    }
}
