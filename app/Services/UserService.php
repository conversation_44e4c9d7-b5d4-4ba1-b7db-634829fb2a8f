<?php

namespace App\Services;

use App\Contracts\UserRepositoryInterface;
use App\Exceptions\BusinessException;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

/**
 * 用户管理服务 - 跨库访问版本（简化版）
 *
 * 处理用户信息管理相关的业务逻辑，当前阶段专注于基础功能
 */
class UserService
{
    /**
     * 用户仓储
     */
    private UserRepositoryInterface $userRepository;

    /**
     * 构造函数
     *
     * @param  UserRepositoryInterface  $userRepository  用户仓储接口
     */
    public function __construct(UserRepositoryInterface $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    /**
     * 获取用户详细信息
     *
     * @param  int  $userId  用户ID
     * @return User 用户对象
     *
     * @throws BusinessException 当用户不存在时
     */
    public function getUserById(int $userId): User
    {
        $user = $this->userRepository->findById($userId);

        if (! $user) {
            throw BusinessException::fromErrorCode('Auth.user_not_found');
        }

        return $user;
    }

    /**
     * 获取当前认证用户信息
     *
     * @return User 当前用户对象
     *
     * @throws BusinessException 当用户未认证时
     */
    public function getCurrentUser(): User
    {
        $user = Auth::user();

        if (! $user) {
            throw BusinessException::fromErrorCode('Auth.user_not_authenticated');
        }

        return $user;
    }

    /**
     * 获取部门用户列表
     *
     * @param  string  $deptId  部门ID
     * @return array<array<string, mixed>> 部门用户列表
     */
    public function getDepartmentUsers(string $deptId): array
    {
        $users = $this->userRepository->getUsersByDepartment($deptId);

        $result = [];
        foreach ($users as $user) {
            $result[] = [
                'id' => $user->id,
                'username' => $user->username,
                'realname' => $user->realname,
                'email' => $user->email,
                'profession' => $user->profession,
                'is_leader' => $user->isLeader(),
                'phone' => $user->phone,
            ];
        }

        return $result;
    }

    /**
     * 获取活跃用户列表
     *
     * @return array<array<string, mixed>> 活跃用户列表
     */
    public function getActiveUsers(): array
    {
        $users = $this->userRepository->getActiveUsers();

        $result = [];
        foreach ($users as $user) {
            $result[] = [
                'id' => $user->id,
                'username' => $user->username,
                'realname' => $user->realname,
                'email' => $user->email,
                'department_id' => $user->dept_id,
                'profession' => $user->profession,
                'is_leader' => $user->isLeader(),
            ];
        }

        return $result;
    }

    /**
     * 检查用户是否存在
     *
     * @param  string  $identifier  用户标识符（用户名或邮箱）
     * @return bool 用户是否存在
     */
    public function userExists(string $identifier): bool
    {
        return $this->userRepository->findByIdentifier($identifier) !== null;
    }

    /**
     * 检查用户是否活跃
     *
     * @param  string  $identifier  用户标识符（用户名或邮箱）
     * @return bool 用户是否活跃
     */
    public function isUserActive(string $identifier): bool
    {
        return $this->userRepository->existsAndActive($identifier);
    }

    /**
     * 根据用户名获取用户
     *
     * @param  string  $username  用户名
     * @return User|null 用户对象或null
     */
    public function getUserByUsername(string $username): ?User
    {
        return $this->userRepository->findByUsername($username);
    }

    /**
     * 根据邮箱获取用户
     *
     * @param  string  $email  邮箱地址
     * @return User|null 用户对象或null
     */
    public function getUserByEmail(string $email): ?User
    {
        return $this->userRepository->findByEmail($email);
    }

    /**
     * 根据标识符获取用户
     *
     * @param  string  $identifier  用户标识符（用户名或邮箱）
     * @return User|null 用户对象或null
     */
    public function getUserByIdentifier(string $identifier): ?User
    {
        return $this->userRepository->findByIdentifier($identifier);
    }

    /**
     * 更新用户最后登录信息
     *
     * @param  int  $userId  用户ID
     * @param  string  $ip  登录IP地址
     * @return bool 更新是否成功
     */
    public function updateLastLogin(int $userId, string $ip): bool
    {
        return $this->userRepository->updateLastLogin($userId, $ip);
    }
}
