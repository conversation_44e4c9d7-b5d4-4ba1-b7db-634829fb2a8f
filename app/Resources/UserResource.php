<?php

namespace App\Resources;

use Illuminate\Http\Request;

/**
 * 用户资源类 - 跨库访问版本
 *
 * @property-read int $id
 * @property-read string $username
 * @property-read string $realname
 * @property-read string $email
 * @property-read string $profession
 * @property-read string $dept_id
 * @property-read bool $is_leader
 * @property-read string $phone
 * @property-read string $name
 * @property-read int $lastlogintime
 * @property-read string $lastloginip
 * @property-read int $inputtime
 * @property-read int $disabled
 */
class UserResource extends BaseResource
{
    /**
     * 将资源转换为数组
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'username' => $this->username,
            'name' => $this->realname ?: $this->username, // 使用realname或username
            'realname' => $this->realname,
            'email' => $this->email,
            'profession' => $this->profession,
            'department_id' => $this->dept_id,
            'is_leader' => (bool) $this->is_leader,
            'phone' => $this->phone,
            'is_active' => $this->disabled === 1,
            'last_login_time' => $this->lastlogintime ?
                date('Y-m-d H:i:s', $this->lastlogintime) : null,
            'last_login_ip' => $this->lastloginip ?: null,
            'created_at' => $this->inputtime ?
                date('Y-m-d H:i:s', $this->inputtime) : null,
        ];
    }
}
