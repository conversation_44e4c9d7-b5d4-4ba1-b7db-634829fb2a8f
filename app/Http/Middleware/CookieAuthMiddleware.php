<?php

namespace App\Http\Middleware;

use App\Services\CookieAuthService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;

/**
 * Cookie认证中间件
 *
 * 从Cookie中读取token并验证用户身份
 */
class CookieAuthMiddleware
{
    /**
     * Cookie认证服务
     */
    private CookieAuthService $cookieAuthService;

    /**
     * 构造函数
     *
     * @param  CookieAuthService  $cookieAuthService  Cookie认证服务
     */
    public function __construct(CookieAuthService $cookieAuthService)
    {
        $this->cookieAuthService = $cookieAuthService;
    }

    /**
     * 处理传入的请求
     *
     * @param  Request  $request  HTTP请求
     * @param  \Closure(Request): mixed  $next  下一个中间件
     */
    public function handle(Request $request, Closure $next): mixed
    {
        // 从Cookie中获取token
        $token = $this->cookieAuthService->getTokenFromRequest($request);

        if ($token) {
            // 验证token并获取用户
            $user = $this->cookieAuthService->validateToken($token);

            if ($user) {
                // 设置认证用户
                Auth::setUser($user);

                // 检查是否需要刷新token（剩余时间少于1小时时刷新）
                $tokenInfo = $this->cookieAuthService->getTokenInfo($token);
                if ($tokenInfo && $this->shouldRefreshToken($tokenInfo)) {
                    $newToken = $this->cookieAuthService->refreshToken($token);
                    if ($newToken) {
                        // 在响应中设置新的cookie
                        $response = $next($request);
                        if ($response instanceof Response) {
                            return $this->cookieAuthService->setAuthCookie($response, $newToken);
                        }
                    }
                }

                return $next($request);
            }
        }

        // 认证失败，返回401错误
        return response()->json([
            'success' => false,
            'message' => '用户未认证',
            'code' => 401,
        ], 401);
    }

    /**
     * 检查是否需要刷新token
     *
     * @param  array{user_id: int|null, username: string|null, expires_at: int|null, issued_at: int|null, is_expired: bool}  $tokenInfo  token信息
     * @return bool 是否需要刷新
     */
    private function shouldRefreshToken(array $tokenInfo): bool
    {
        $expiresAt = $tokenInfo['expires_at'] ?? 0;
        $currentTimestamp = now()->timestamp;

        // 计算剩余时间
        $remainingTime = (int) $expiresAt - (int) $currentTimestamp;

        // 剩余时间少于1小时时刷新token
        return $remainingTime < 3600;
    }
}
