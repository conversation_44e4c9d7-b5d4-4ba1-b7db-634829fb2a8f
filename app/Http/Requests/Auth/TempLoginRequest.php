<?php

namespace App\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 临时登录请求验证类
 *
 * 验证临时登录接口的输入参数
 */
class TempLoginRequest extends FormRequest
{
    /**
     * 确定用户是否有权限进行此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取应用于请求的验证规则
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'identifier' => [
                'required',
                'string',
                'max:150',
            ],
        ];
    }

    /**
     * 获取验证错误的自定义消息
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'identifier.required' => '用户标识符不能为空',
            'identifier.string' => '用户标识符必须是字符串',
            'identifier.max' => '用户标识符长度不能超过150个字符',
        ];
    }

    /**
     * 获取验证字段的自定义属性名称
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'identifier' => '用户标识符',
        ];
    }
}
