<?php

namespace App\Http\Controllers;

use App\ApiResponses\ApiResponse;
use App\Http\Requests\Auth\TempLoginRequest;
use App\Resources\UserResource;
use App\Services\CookieAuthService;
use App\Services\TempAuthService;
use App\Services\UserService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * 认证控制器
 *
 * 处理用户认证相关的HTTP请求
 */
class AuthController extends Controller
{
    /**
     * 临时认证服务
     */
    protected TempAuthService $tempAuthService;

    /**
     * 用户服务
     */
    protected UserService $userService;

    /**
     * Cookie认证服务
     */
    protected CookieAuthService $cookieAuthService;

    /**
     * 构造函数
     *
     * @param  TempAuthService  $tempAuthService  临时认证服务
     * @param  UserService  $userService  用户服务
     * @param  CookieAuthService  $cookieAuthService  Cookie认证服务
     */
    public function __construct(
        TempAuthService $tempAuthService,
        UserService $userService,
        CookieAuthService $cookieAuthService
    ) {
        $this->tempAuthService = $tempAuthService;
        $this->userService = $userService;
        $this->cookieAuthService = $cookieAuthService;
    }

    /**
     * 临时登录接口（开发测试用）
     *
     * @param  TempLoginRequest  $request  登录请求
     * @return JsonResponse 认证结果
     */
    public function tempLogin(TempLoginRequest $request): JsonResponse
    {
        $identifier = $request->validated()['identifier'];

        // 执行临时登录
        $authResult = $this->tempAuthService->tempLogin($identifier);

        // 创建响应
        $response = ApiResponse::success($authResult->toArray(), '临时登录成功');

        // 设置认证Cookie
        $responseWithCookie = $this->cookieAuthService->setAuthCookie(
            $response,
            $authResult->token,
            $authResult->expiresIn ?? 480
        );

        // 确保返回JsonResponse类型
        return $responseWithCookie instanceof JsonResponse ? $responseWithCookie : $response;
    }

    /**
     * 用户登出接口
     *
     * @param  Request  $request  HTTP请求
     * @return JsonResponse 登出结果
     */
    public function logout(Request $request): JsonResponse
    {
        $user = $request->user();

        if ($user) {
            // 执行登出逻辑
            $this->tempAuthService->logout($user);
        }

        // 创建响应
        $response = ApiResponse::success(null, '登出成功');

        // 清除认证Cookie
        $responseWithCookie = $this->cookieAuthService->clearAuthCookie($response);

        // 确保返回JsonResponse类型
        return $responseWithCookie instanceof JsonResponse ? $responseWithCookie : $response;
    }

    /**
     * 获取当前用户信息
     *
     * @param  Request  $request  HTTP请求
     * @return JsonResponse 用户信息
     */
    public function profile(Request $request): JsonResponse
    {
        $user = $this->userService->getCurrentUser();

        return ApiResponse::success(
            new UserResource($user),
            '获取用户信息成功'
        );
    }

    /**
     * SSO登录接口（预留）
     *
     * @param  Request  $request  SSO登录请求
     * @return JsonResponse 认证结果
     */
    public function ssoLogin(Request $request): JsonResponse
    {
        // TODO: 实现SSO登录逻辑
        return ApiResponse::error('SSO登录功能暂未实现', 501);
    }
}
