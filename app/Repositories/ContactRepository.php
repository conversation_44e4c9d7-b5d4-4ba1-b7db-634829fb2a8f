<?php

namespace App\Repositories;

use App\Contracts\QueryBuilderInterface;
use App\Contracts\TransactionManagerInterface;
use App\Models\Contact;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;

/**
 * 联系人仓储实现
 *
 * 集成数据库操作优化组件，提供高性能的数据库操作
 */
class ContactRepository implements ContactRepositoryInterface
{
    /**
     * Contact 模型实例
     */
    protected Contact $model;

    /**
     * 查询构建器实例
     */
    protected QueryBuilderInterface $queryBuilder;

    /**
     * 事务管理器实例
     */
    protected TransactionManagerInterface $transactionManager;

    /**
     * 构造函数
     */
    public function __construct(
        Contact $model,
        QueryBuilderInterface $queryBuilder,
        TransactionManagerInterface $transactionManager
    ) {
        $this->model = $model;
        $this->queryBuilder = $queryBuilder;
        $this->transactionManager = $transactionManager;
    }

    /**
     * 获取联系人分页列表（优化版本）
     *
     * @param  array  $filters  筛选条件
     * @param  int  $perPage  每页数量
     * @param  int  $page  页码
     */
    public function getContactsList(array $filters = [], int $perPage = 15, int $page = 1): LengthAwarePaginator
    {
        try {
            $query = $this->model->newQuery();

            // 预加载关联关系
            $query->with(['leads:id,company_full_name']);

            // 使用增强查询构建器构建复杂查询条件
            $conditions = $this->buildContactQueryConditions($filters);
            $query = $this->queryBuilder->buildComplexQuery($conditions, $query);

            // 添加动态排序
            $sortRules = $this->buildContactSortRules($filters);
            $query = $this->queryBuilder->addDynamicSorting($query, $sortRules);

            // 优化查询性能
            $optimizationOptions = [
                'optimize_select' => true,
                'cache' => config('database_optimization.query_builder.cache.enabled', false),
                'cache_ttl' => 1200, // 20分钟缓存
            ];
            $query = $this->queryBuilder->optimizeQuery($query, $optimizationOptions);

            // 构建分页查询
            return $this->queryBuilder->buildPaginatedQuery($query, $page, $perPage);

        } catch (\Exception $e) {
            Log::error('获取联系人列表失败', [
                'filters' => $filters,
                'error' => $e->getMessage(),
            ]);

            // 降级处理
            return $this->getFallbackContactsList($filters, $perPage, $page);
        }
    }

    /**
     * 根据ID获取联系人详情（优化版本）
     *
     * @param  int  $id  联系人ID
     */
    public function findById(int $id): ?Contact
    {
        try {
            $query = $this->model->newQuery();
            $query->with(['leads:id,company_full_name']);

            $conditions = ['id' => $id];
            $query = $this->queryBuilder->buildComplexQuery($conditions, $query);

            // 设置查询缓存
            $query = $this->queryBuilder->setCacheForQuery($query, 3600, "contact_detail_{$id}");

            return $query->first();

        } catch (\Exception $e) {
            Log::error('获取联系人详情失败', [
                'id' => $id,
                'error' => $e->getMessage(),
            ]);

            // 降级处理
            return $this->model->with(['leads:id,company_full_name'])->find($id);
        }
    }

    /**
     * 根据手机号获取联系人（优化版本）
     *
     * @param  string  $mobile  手机号
     */
    public function findByMobile(string $mobile): ?Contact
    {
        try {
            $query = $this->model->newQuery();
            $conditions = ['mobile' => $mobile];
            $query = $this->queryBuilder->buildComplexQuery($conditions, $query);

            // 设置短期缓存
            $query = $this->queryBuilder->setCacheForQuery($query, 600, "contact_mobile_{$mobile}");

            return $query->first();

        } catch (\Exception $e) {
            Log::warning('根据手机号获取联系人失败', [
                'mobile' => $mobile,
                'error' => $e->getMessage(),
            ]);

            // 降级处理
            return $this->model->where('mobile', $mobile)->first();
        }
    }

    /**
     * 创建联系人（使用事务管理）
     *
     * @param  array  $data  联系人数据
     */
    public function create(array $data): Contact
    {
        return $this->transactionManager->executeInTransaction(function () use ($data) {
            $contact = $this->model->create($data);

            // 清除相关缓存
            $this->clearRelatedCache();

            Log::info('联系人创建成功', [
                'contact_id' => $contact->id,
                'name' => $contact->name,
                'mobile' => $contact->mobile,
            ]);

            return $contact;
        });
    }

    /**
     * 更新联系人（使用事务管理）
     *
     * @param  int  $id  联系人ID
     * @param  array  $data  更新数据
     */
    public function update(int $id, array $data): bool
    {
        return $this->transactionManager->executeInTransaction(function () use ($id, $data) {
            $result = $this->model->where('id', $id)->update($data);

            if ($result > 0) {
                // 清除相关缓存
                $this->clearRelatedCache($id);

                Log::info('联系人更新成功', [
                    'contact_id' => $id,
                    'updated_fields' => array_keys($data),
                ]);
            }

            return $result > 0;
        });
    }

    /**
     * 删除联系人（使用事务管理）
     *
     * @param  int  $id  联系人ID
     */
    public function delete(int $id): bool
    {
        return $this->transactionManager->executeInTransaction(function () use ($id) {
            $contact = $this->model->find($id);

            if (! $contact) {
                return false;
            }

            $result = $contact->delete();

            if ($result) {
                // 清除相关缓存
                $this->clearRelatedCache($id);

                Log::info('联系人删除成功', [
                    'contact_id' => $id,
                    'name' => $contact->name,
                ]);
            }

            return $result ?? false;
        });
    }

    /**
     * 检查手机号是否已存在
     *
     * @param  string  $mobile  手机号
     * @param  int|null  $excludeId  排除的联系人ID
     */
    public function existsByMobile(string $mobile, ?int $excludeId = null): bool
    {
        $query = $this->model->where('mobile', $mobile);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * 批量创建联系人
     *
     * @param  array  $contactsData  联系人数据数组
     */
    public function createBatch(array $contactsData): Collection
    {
        $contacts = collect();

        foreach ($contactsData as $contactData) {
            $contacts->push($this->create($contactData));
        }

        return $contacts;
    }

    /**
     * 根据线索ID获取关联的联系人
     *
     * @param  int  $leadId  线索ID
     */
    public function getContactsByLeadId(int $leadId): Collection
    {
        return $this->model->whereHas('leads', function ($query) use ($leadId) {
            $query->where('crm_lead.id', $leadId);
        })->get();
    }

    /**
     * 搜索联系人（按姓名、手机号）优化版本
     *
     * @param  string  $keyword  搜索关键词
     * @param  int  $limit  限制数量
     */
    public function searchContacts(string $keyword, int $limit = 10): Collection
    {
        try {
            $query = $this->model->newQuery();

            // 使用搜索条件
            $query = $this->queryBuilder->addSearchCondition(
                $query,
                ['name', 'mobile'],
                $keyword,
                'like'
            );

            // 设置查询缓存
            $query = $this->queryBuilder->setCacheForQuery($query, 300, "contact_search_{$keyword}_{$limit}");

            return $query->limit($limit)->get();

        } catch (\Exception $e) {
            Log::warning('搜索联系人失败', [
                'keyword' => $keyword,
                'limit' => $limit,
                'error' => $e->getMessage(),
            ]);

            // 降级处理
            return $this->model->where(function ($query) use ($keyword) {
                $query->where('name', 'like', "%{$keyword}%")
                    ->orWhere('mobile', 'like', "%{$keyword}%");
            })->limit($limit)->get();
        }
    }

    /**
     * 构建联系人查询条件数组
     */
    protected function buildContactQueryConditions(array $filters): array
    {
        $conditions = [];

        // 基础字段条件
        if (! empty($filters['gender'])) {
            $conditions['gender'] = $filters['gender'];
        }

        // 搜索条件
        $searchFields = [];
        if (! empty($filters['name'])) {
            $searchFields['name'] = $filters['name'];
        }
        if (! empty($filters['mobile'])) {
            $searchFields['mobile'] = $filters['mobile'];
        }
        if (! empty($filters['email'])) {
            $searchFields['email'] = $filters['email'];
        }
        if (! empty($filters['department'])) {
            $searchFields['department'] = $filters['department'];
        }
        if (! empty($filters['position'])) {
            $searchFields['position'] = $filters['position'];
        }

        // 合并搜索条件
        if (! empty($searchFields)) {
            foreach ($searchFields as $field => $value) {
                $conditions["search_{$field}"] = [
                    'keyword' => $value,
                    'fields' => [$field],
                    'match_type' => 'like',
                ];
            }
        }

        return $conditions;
    }

    /**
     * 构建联系人排序规则数组
     */
    protected function buildContactSortRules(array $filters): array
    {
        return [
            [
                'field' => $filters['sort_by'] ?? 'created_at',
                'direction' => $filters['sort_direction'] ?? 'desc',
                'nulls' => 'last',
            ],
        ];
    }

    /**
     * 降级处理：使用原始查询方式
     */
    protected function getFallbackContactsList(array $filters, int $perPage, int $page): LengthAwarePaginator
    {
        $query = $this->model->newQuery();
        $query->with(['leads:id,company_full_name']);

        // 简单的条件构建
        if (! empty($filters['name'])) {
            $query->where('name', 'like', "%{$filters['name']}%");
        }
        if (! empty($filters['mobile'])) {
            $query->where('mobile', 'like', "%{$filters['mobile']}%");
        }
        if (! empty($filters['email'])) {
            $query->where('email', 'like', "%{$filters['email']}%");
        }
        if (! empty($filters['department'])) {
            $query->where('department', 'like', "%{$filters['department']}%");
        }
        if (! empty($filters['position'])) {
            $query->where('position', 'like', "%{$filters['position']}%");
        }
        if (! empty($filters['gender'])) {
            $query->where('gender', $filters['gender']);
        }

        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortBy, $sortDirection);

        return $query->paginate($perPage, ['*'], 'page', $page);
    }

    /**
     * 清除相关缓存
     *
     * @param  int|null  $contactId  联系人ID
     */
    protected function clearRelatedCache(?int $contactId = null): void
    {
        try {
            $cacheKeys = ['contacts_list_*', 'contact_search_*'];

            if ($contactId) {
                $cacheKeys[] = "contact_detail_{$contactId}";
            }

            foreach ($cacheKeys as $pattern) {
                $this->queryBuilder->clearQueryCache($pattern);
            }

        } catch (\Exception $e) {
            Log::warning('清除联系人缓存失败', [
                'contact_id' => $contactId,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
