<?php

namespace App\Repositories;

use App\Contracts\UserRepositoryInterface;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * 用户仓储实现 - 跨库访问版本
 *
 * 实现用户数据访问逻辑，支持缓存和跨库访问
 */
class UserRepository implements UserRepositoryInterface
{
    /**
     * 缓存前缀
     */
    private const CACHE_PREFIX = 'user_system:';

    /**
     * 缓存时间（秒）
     */
    private const CACHE_TTL = 3600;

    /**
     * 根据ID查找用户
     */
    public function findById(int $id): ?User
    {
        $cacheKey = self::CACHE_PREFIX."id:{$id}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($id) {
            return User::find($id);
        });
    }

    /**
     * 根据用户名查找用户
     */
    public function findByUsername(string $username): ?User
    {
        $cacheKey = self::CACHE_PREFIX."username:{$username}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($username) {
            return User::findByUsername($username);
        });
    }

    /**
     * 根据邮箱查找用户
     */
    public function findByEmail(string $email): ?User
    {
        $cacheKey = self::CACHE_PREFIX."email:{$email}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($email) {
            return User::findByEmail($email);
        });
    }

    /**
     * 根据标识符查找用户（用户名或邮箱）
     */
    public function findByIdentifier(string $identifier): ?User
    {
        // 先尝试用户名查找
        $user = $this->findByUsername($identifier);

        // 如果没找到，再尝试邮箱查找
        if (! $user && filter_var($identifier, FILTER_VALIDATE_EMAIL)) {
            $user = $this->findByEmail($identifier);
        }

        return $user;
    }

    /**
     * 检查用户是否存在且活跃
     */
    public function existsAndActive(string $identifier): bool
    {
        $user = $this->findByIdentifier($identifier);

        return $user && $user->isActive();
    }

    /**
     * 获取活跃用户列表
     */
    public function getActiveUsers(): Collection
    {
        $cacheKey = self::CACHE_PREFIX.'active_users';

        return Cache::remember($cacheKey, self::CACHE_TTL, function () {
            return User::active()->get();
        });
    }

    /**
     * 根据部门获取用户
     */
    public function getUsersByDepartment(string $deptId): Collection
    {
        $cacheKey = self::CACHE_PREFIX."dept:{$deptId}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($deptId) {
            return User::byDepartment($deptId)->active()->get();
        });
    }

    /**
     * 更新用户最后登录信息
     */
    public function updateLastLogin(int $userId, string $ip): bool
    {
        try {
            $result = User::where('id', $userId)->update([
                'lastloginip' => $ip,
                'lastlogintime' => time(),
            ]);

            if ($result) {
                // 清除相关缓存
                $this->clearUserCache($userId);

                Log::info('用户登录信息更新成功', [
                    'user_id' => $userId,
                    'ip' => $ip,
                ]);
            }

            return (bool) $result;
        } catch (\Exception $e) {
            Log::error('更新用户登录信息失败', [
                'user_id' => $userId,
                'ip' => $ip,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 更新用户token信息
     */
    public function updateToken(int $userId, string $token, ?\DateTime $expireAt = null): bool
    {
        try {
            $data = ['token' => $token];

            if ($expireAt) {
                $data['token_expire_at'] = $expireAt;
            }

            $result = User::where('id', $userId)->update($data);

            if ($result) {
                $this->clearUserCache($userId);
            }

            return (bool) $result;
        } catch (\Exception $e) {
            Log::error('更新用户token失败', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 清除用户token
     */
    public function clearToken(int $userId): bool
    {
        try {
            $result = User::where('id', $userId)->update([
                'token' => '',
                'token_expire_at' => null,
            ]);

            if ($result) {
                $this->clearUserCache($userId);
            }

            return (bool) $result;
        } catch (\Exception $e) {
            Log::error('清除用户token失败', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 清除用户相关缓存
     *
     * @param  int  $userId  用户ID
     */
    private function clearUserCache(int $userId): void
    {
        $user = User::find($userId);

        if ($user) {
            Cache::forget(self::CACHE_PREFIX."id:{$userId}");
            Cache::forget(self::CACHE_PREFIX."username:{$user->username}");
            Cache::forget(self::CACHE_PREFIX."email:{$user->email}");
        }

        // 清除列表缓存
        Cache::forget(self::CACHE_PREFIX.'active_users');

        if ($user && $user->dept_id) {
            Cache::forget(self::CACHE_PREFIX."dept:{$user->dept_id}");
        }
    }
}
