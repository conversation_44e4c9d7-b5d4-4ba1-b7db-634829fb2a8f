<?php

namespace App\Repositories;

use App\Models\LeadUserRelation;
use App\Models\User;
use App\Services\CrossDatabaseRelationService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

/**
 * 线索用户关联仓储实现
 */
class LeadUserRelationRepository implements LeadUserRelationRepositoryInterface
{
    /**
     * 角色类型常量
     */
    public const ROLE_TYPE_OWNER = 1;

    public const ROLE_TYPE_COLLABORATOR = 2;      // 负责人

    protected LeadUserRelation $model; // 协同人

    /**
     * 跨数据库关联服务
     */
    protected CrossDatabaseRelationService $crossDbService;

    /**
     * 构造函数
     */
    public function __construct(
        LeadUserRelation $model,
        CrossDatabaseRelationService $crossDbService
    ) {
        $this->model = $model;
        $this->crossDbService = $crossDbService;
    }

    /**
     * 根据线索ID获取用户关联关系
     *
     * @param  int  $leadId  线索ID
     */
    public function getRelationsByLeadId(int $leadId): Collection
    {
        $relations = $this->model->where('lead_id', $leadId)->get();

        if ($relations->isEmpty()) {
            return $relations;
        }

        // 获取用户信息
        $userIds = $relations->pluck('user_id')->unique()->toArray();
        $users = User::whereIn('id', $userIds)
            ->select(['id', 'username', 'realname', 'email'])
            ->get()
            ->keyBy('id');

        // 为每个关联关系设置用户信息
        $relations->each(function ($relation) use ($users) {
            $user = $users->get($relation->user_id);
            if ($user) {
                $relation->setRelation('user', $user);
            }
        });

        return $relations;
    }

    /**
     * 根据线索ID获取所有用户关联（接口方法）
     *
     * @param  int  $leadId  线索ID
     * @return Collection 用户关联集合
     */
    public function getByLeadId(int $leadId): Collection
    {
        return $this->getRelationsByLeadId($leadId);
    }

    /**
     * 根据用户ID获取线索关联关系
     *
     * @param  int  $userId  用户ID
     */
    public function getRelationsByUserId(int $userId): Collection
    {
        return $this->model->with(['Lead'])
            ->where('user_id', $userId)
            ->get();
    }

    /**
     * 获取线索的主负责人
     *
     * @param  int  $leadId  线索ID
     */
    public function getPrimaryOwnerByLeadId(int $leadId): ?LeadUserRelation
    {
        $relation = $this->model->where('lead_id', $leadId)
            ->where('role_type', self::ROLE_TYPE_OWNER)
            ->where('is_primary', 1)
            ->first();

        if ($relation) {
            $user = User::find($relation->user_id);
            if ($user) {
                $relation->setRelation('user', $user);
            }
        }

        return $relation;
    }

    /**
     * 获取线索的协同人员
     *
     * @param  int  $leadId  线索ID
     */
    public function getCollaboratorsByLeadId(int $leadId): Collection
    {
        $relations = $this->model->where('lead_id', $leadId)
            ->where('role_type', self::ROLE_TYPE_COLLABORATOR)
            ->get();

        if ($relations->isEmpty()) {
            return $relations;
        }

        // 获取用户信息
        $userIds = $relations->pluck('user_id')->unique()->toArray();
        $users = User::whereIn('id', $userIds)
            ->select(['id', 'username', 'realname', 'email'])
            ->get()
            ->keyBy('id');

        // 为每个关联关系设置用户信息
        $relations->each(function ($relation) use ($users) {
            $user = $users->get($relation->user_id);
            if ($user) {
                $relation->setRelation('user', $user);
            }
        });

        return $relations;
    }

    /**
     * 批量创建线索用户关联关系
     *
     * @param  array  $relationsData  关联数据数组
     */
    public function createBatch(array $relationsData): Collection
    {
        $relations = collect();

        foreach ($relationsData as $relationData) {
            $relations->push($this->create($relationData));
        }

        return $relations;
    }

    /**
     * 创建线索用户关联关系
     *
     * @param  array  $data  关联数据
     */
    public function create(array $data): LeadUserRelation
    {
        return $this->model->create($data);
    }

    /**
     * 删除线索的所有用户关联关系
     *
     * @param  int  $leadId  线索ID
     */
    public function deleteByLeadId(int $leadId): bool
    {
        return $this->model->where('lead_id', $leadId)->delete() !== false;
    }

    /**
     * 删除关联关系
     *
     * @param  int  $id  关联ID
     */
    public function delete(int $id): bool
    {
        $relation = $this->model->find($id);
        if ($relation) {
            return $relation->delete() ?? false;
        }

        return false;
    }

    /**
     * 检查用户是否已关联到线索
     *
     * @param  int  $leadId  线索ID
     * @param  int  $userId  用户ID
     * @param  int  $roleType  角色类型
     */
    public function existsRelation(int $leadId, int $userId, int $roleType): bool
    {
        return $this->model->where('lead_id', $leadId)
            ->where('user_id', $userId)
            ->where('role_type', $roleType)
            ->exists();
    }

    /**
     * 设置线索的主负责人
     *
     * @param  int  $leadId  线索ID
     * @param  int  $userId  用户ID
     */
    public function setPrimaryOwner(int $leadId, int $userId): bool
    {
        return DB::transaction(function () use ($leadId, $userId) {
            // 先将该线索的所有负责人设为非主负责人
            $this->model->where('lead_id', $leadId)
                ->where('role_type', self::ROLE_TYPE_OWNER)
                ->update(['is_primary' => 0]);

            // 设置新的主负责人
            $relation = $this->findRelation($leadId, $userId, self::ROLE_TYPE_OWNER);

            if ($relation) {
                return $this->update($relation->id, ['is_primary' => 1]);
            } else {
                // 如果关联关系不存在，创建新的主负责人关联
                $this->create([
                    'lead_id' => $leadId,
                    'user_id' => $userId,
                    'role_type' => self::ROLE_TYPE_OWNER,
                    'is_primary' => 1,
                ]);

                return true;
            }
        });
    }

    /**
     * 更新关联关系
     *
     * @param  int  $id  关联ID
     * @param  array  $data  更新数据
     */
    public function update(int $id, array $data): bool
    {
        return $this->model->where('id', $id)->update($data) > 0;
    }

    /**
     * 根据条件查找关联关系
     *
     * @param  int  $leadId  线索ID
     * @param  int  $userId  用户ID
     * @param  int  $roleType  角色类型
     */
    public function findRelation(int $leadId, int $userId, int $roleType): ?LeadUserRelation
    {
        return $this->model->where('lead_id', $leadId)
            ->where('user_id', $userId)
            ->where('role_type', $roleType)
            ->first();
    }

    /**
     * 检查用户是否为线索负责人
     *
     * @param  int  $leadId  线索ID
     * @param  int  $userId  用户ID
     * @return bool 是否为负责人
     */
    public function isOwner(int $leadId, int $userId): bool
    {
        return $this->model->where('lead_id', $leadId)
            ->where('user_id', $userId)
            ->where('role_type', self::ROLE_TYPE_OWNER)
            ->exists();
    }

    /**
     * 检查用户是否为线索协同人
     *
     * @param  int  $leadId  线索ID
     * @param  int  $userId  用户ID
     * @return bool 是否为协同人
     */
    public function isCollaborator(int $leadId, int $userId): bool
    {
        return $this->model->where('lead_id', $leadId)
            ->where('user_id', $userId)
            ->where('role_type', self::ROLE_TYPE_COLLABORATOR)
            ->exists();
    }
}
