---
description:
globs:
alwaysApply: true
---

# 需求
- 不要单独创建分支开发
- 每个需求要生成对应的任务文档合理命名为xx-task.md，存储在.cursor/tasks目录下
- 完成任务后生成文档在docs目录下



# Laravel 项目编码规范

## 基本要求

-   使用中文进行交流和文档编写
-   文档输出简洁明了，不使用表情符号
-   严格遵循 PSR-12 编码标准和 Laravel 命名约定
-   环境变量修改统一在.local.env文件下

## 架构设计原则

### SOLID 原则

-   **单一职责原则**：每个类和函数只承担一个明确的职责
-   **开闭原则**：对扩展开放，对修改封闭
-   **里氏替换原则**：子类可以替换父类而不影响程序正确性
-   **接口隔离原则**：依赖于抽象接口而非具体实现
-   **依赖倒置原则**：高层模块不依赖低层模块，都依赖于抽象

### 分层架构

严格按照以下层次组织代码：

1. **Controller 层**：处理 HTTP 请求，调用 Service 层
2. **Request 层**：验证和格式化输入数据
3. **Service 层**：业务逻辑处理，协调 Repository 和其他服务
4. **Repository 层**：数据访问抽象，隔离数据源
5. **Model 层**：数据模型和关系定义
6. **Resource 层**：API 响应数据格式化

## 代码组织规范

### 目录结构

```
app/
├── Http/
│   ├── Controllers/     # 控制器
│   ├── Requests/        # 请求验证
│   └── Middleware/      # 中间件
├── Services/            # 业务服务
├── Repositories/        # 数据仓储
├── Models/              # 数据模型
├── Resources/           # API资源
├── DTOs/                # 数据传输对象
├── Exceptions/          # 自定义异常
└── Contracts/           # 接口定义
```



### 命名约定

-   **类名**：使用 PascalCase（如：`LeadService`）
-   **方法名**：使用 camelCase（如：`getLeadList`）
-   **变量名**：使用 camelCase（如：`$leadData`）
-   **常量名**：使用 UPPER_SNAKE_CASE（如：`MAX_RETRY_COUNT`）
-   **数据库表名**：使用 snake_case 复数形式（如：`lead_contacts`）

### 接口和实现

-   所有 Repository 必须定义接口
-   Service 层通过依赖注入使用 Repository 接口
-   在 ServiceProvider 中绑定接口与实现

## 代码质量要求

### 文档和类型约束

-   所有类、方法必须包含完整的 PHPDoc 注释，注释不用包含作者和时间
-   使用 PHP 类型声明（参数类型、返回类型）
-   为复杂业务逻辑添加详细注释

### 异常处理

-   使用自定义异常类处理业务异常
-   在 Controller 层统一处理异常响应
-   记录必要的错误日志

### API 响应规范

-   使用统一的 API 响应格式
-   通过 Resource 类格式化输出数据
-   包含适当的 HTTP 状态码

## 开发流程

### 功能开发步骤

1. 创建或更新 Model，不用创建Migration
2. 定义 Repository 接口和实现
3. 创建 Service 类处理业务逻辑
4. 创建 Request 类验证输入
5. 创建 Controller 处理 HTTP 请求
6. 创建 Resource 格式化响应
7. 定义路由

### 代码审查要点

-   检查是否遵循分层架构
-   验证异常处理是否完整
-   确认类型约束和文档完整性
-   检查命名是否符合约定

## 测试策略

-   执行完每个开发任务后不需要立即编写单元测试
-   重点关注功能实现的正确性和代码质量

## 性能和安全

-   使用 Eloquent ORM 的最佳实践
-   避免 N+1 查询问题
-   使用 Laravel 内置的安全特性
