
### 数据库名称
crs-api
### mysql DDL

CREATE TABLE `crs_system_user` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(100) NOT NULL DEFAULT '' COMMENT '用户名',
  `password` char(32) NOT NULL DEFAULT '',
  `realname` varchar(100) NOT NULL DEFAULT '' COMMENT '真实姓名',
  `email` varchar(150) NOT NULL DEFAULT '',
  `lastloginip` varchar(15) NOT NULL DEFAULT '' COMMENT '登录IP',
  `lastlogintime` int unsigned NOT NULL DEFAULT '0' COMMENT '最后登录时间',
  `disabled` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '是否禁用 1 启用 2 禁用',
  `inputtime` int unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  `profession` varchar(100) NOT NULL DEFAULT '' COMMENT '职位',
  `dept_id` varchar(20) NOT NULL DEFAULT '' COMMENT '所属部门ID',
  `is_leader` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否为leader',
  `phone` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
  `surplus_auth` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '余量预警权限',
  `expire_auth` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '账号到期权限',
  `balance_auth` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '余额预警权限',
  `daily_auth` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '日报权限',
  `data_auth` varchar(50) NOT NULL DEFAULT '0' COMMENT '数据权限',
  `profit_auth` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '利润权责邮件权限',
  `source_auth` varchar(50) NOT NULL DEFAULT '-1' COMMENT '数据来源权限',
  `token` char(32) NOT NULL DEFAULT '' COMMENT 'token',
  `token_expire_at` datetime DEFAULT NULL COMMENT 'token截止日期',
  `fs_user_id` varchar(32) NOT NULL DEFAULT '' COMMENT '飞书user_id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `username` (`username`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=211 DEFAULT CHARSET=utf8mb3 COMMENT='系统用户表'